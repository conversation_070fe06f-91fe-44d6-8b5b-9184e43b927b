# RADIUS AAA 安全部署指南

## 🔐 概述

本指南介绍如何使用Nuitka将RADIUS AAA模块的Python代码编译为机器码，实现最高级别的代码保密，并集成到现有的SmartAAA系统中。

## 🎯 安全特性

### ✅ 代码保密
- **完全编译**：Python源码编译为C++机器码
- **无法逆向**：生成的可执行文件不包含Python字节码
- **零源码泄露**：即使获得可执行文件也无法还原源代码

### ✅ 容器安全
- **非特权用户**：容器内使用非root用户运行
- **只读文件系统**：生产环境启用只读根文件系统
- **最小权限**：仅保留必要的系统权限
- **安全选项**：启用多项Docker安全特性

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保Docker和docker-compose已安装
docker --version
docker-compose --version

# 克隆项目（如果还没有）
cd /data/project/smartaaa
```

### 2. 一键部署

```bash
# 给脚本执行权限
chmod +x deploy_smartaaa.sh
chmod +x radius/build_secure.sh

# 构建并部署所有服务
./deploy_smartaaa.sh deploy -e prod

# 或者分步执行
./deploy_smartaaa.sh build -e prod    # 构建镜像
./deploy_smartaaa.sh start            # 启动服务
```

### 3. 验证部署

```bash
# 查看服务状态
./deploy_smartaaa.sh status

# 查看日志
./deploy_smartaaa.sh logs radius-aaa
```

## 📋 详细部署步骤

### 步骤1：构建RADIUS安全镜像

```bash
cd radius

# 使用Nuitka构建安全镜像
./build_secure.sh -t v1.0 -e prod -v

# 手动构建 radius 镜像
docker build   --progress=plain   --no-cache   --build-arg https_proxy=http://172.27.0.1:10808/   --build-arg http_proxy=http://172.27.0.1:10808/   -f radius/docker/Dockerfile_nuitka   -t radius-aaa-secure:latest   radius/

# 查看构建结果
docker images radius-aaa-secure
```

### 步骤2：配置服务

编辑配置文件：
```bash
# 数据库配置
vim radius/conf/db.ini

# RADIUS配置
vim radius/conf/aaa_config.ini
```

### 步骤3：启动集成服务

```bash
# 创建Docker网络
docker network create --driver bridge --subnet=**********/16 aaa-net

# 启动所有服务
docker-compose -f docker-compose-integrated.yml up -d
```

## 🔧 配置说明

### 网络配置

| 服务 | 容器名 | IP地址 | 端口 |
|------|--------|--------|------|
| Web前端 | ynyb-web | ********** | 8080 |
| Web后端 | ynyb-backend | ********** | 8000 |
| RADIUS AAA | radius-aaa-secure | ********** | 1812/1813/3799 |

### 端口映射

- **8080**: Web前端界面
- **8000**: Web后端API
- **1812/udp**: RADIUS认证端口
- **1813/udp**: RADIUS计费端口
- **3799/udp**: RADIUS CoA端口

### 数据卷

```yaml
volumes:
  # RADIUS配置（只读）
  - ./radius/conf:/app/conf:ro
  # RADIUS日志（可写）
  - ./radius/logs:/app/logs
  # Web后端代码
  - ./web/backend:/backend
  # Web前端代码
  - ./web/web:/web
```

## 🛠️ 管理命令

### 服务管理

```bash
# 启动所有服务
./deploy_smartaaa.sh start

# 停止所有服务
./deploy_smartaaa.sh stop

# 重启所有服务
./deploy_smartaaa.sh restart

# 查看服务状态
./deploy_smartaaa.sh status
```

### 日志查看

```bash
# 查看所有服务日志
./deploy_smartaaa.sh logs

# 查看特定服务日志
./deploy_smartaaa.sh logs radius-aaa
./deploy_smartaaa.sh logs ynyb-backend
./deploy_smartaaa.sh logs ynyb-web
```

### 调试和维护

```bash
# 进入RADIUS容器（注意：只读文件系统）
docker exec -it radius-aaa-secure /bin/sh

# 查看RADIUS进程
docker exec radius-aaa-secure ps aux

# 查看网络连接
docker exec radius-aaa-secure netstat -tulpn
```

## 🔍 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -af
   docker builder prune -af
   
   # 重新构建
   ./radius/build_secure.sh -c -t latest -e prod
   ```

2. **服务无法启动**
   ```bash
   # 检查日志
   docker-compose -f docker-compose-integrated.yml logs
   
   # 检查网络
   docker network ls
   docker network inspect aaa-net
   ```

3. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep -E "(8080|8000|1812|1813|3799)"
   
   # 修改docker-compose.yml中的端口映射
   ```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看RADIUS服务性能
docker exec radius-aaa-secure top

# 查看网络流量
docker exec radius-aaa-secure iftop
```

## 🔒 安全建议

### 生产环境安全

1. **网络安全**
   - 配置防火墙规则
   - 使用VPN或专线连接
   - 启用网络隔离

2. **数据安全**
   - 加密敏感配置文件
   - 定期备份数据
   - 使用强密码策略

3. **监控告警**
   - 配置日志监控
   - 设置性能告警
   - 监控安全事件

### 更新维护

```bash
# 更新镜像
./deploy_smartaaa.sh build -e prod

# 滚动更新
docker-compose -f docker-compose-integrated.yml up -d --no-deps radius-aaa

# 备份配置
tar -czf smartaaa-config-$(date +%Y%m%d).tar.gz radius/conf web/backend/config
```

## 📞 支持

如有问题，请检查：
1. Docker和docker-compose版本兼容性
2. 系统资源是否充足
3. 网络端口是否可用
4. 配置文件格式是否正确

---

**注意**: 此部署方案已通过Nuitka编译实现代码保密，生成的可执行文件无法逆向获取源代码。
