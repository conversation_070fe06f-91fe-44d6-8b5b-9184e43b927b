[mysqld]
#
# Remove leading # and set to the amount of RAM for the most important data
# cache in MySQL. Start at 70% of total RAM for dedicated server, else 10%.
# innodb_buffer_pool_size = 128M
#
# Remove leading # to turn on a very important data integrity option: logging
# changes to the binary log between backups.
# log_bin
#
# Remove leading # to set options mainly useful for reporting servers.
# The server defaults are faster for transactions and fast SELECTs.
# Adjust sizes as needed, experiment to find the optimal values.
# join_buffer_size = 128M
# sort_buffer_size = 2M
# read_rnd_buffer_size = 2M
port=3306
# datadir=/data/mysql/data
# socket=/data/mysql/mysql.sock
wait_timeout=2880000
interactive_timeout=2880000
lower_case_table_names=1
max_connections=1000
max_connect_errors=300
innodb_flush_log_at_trx_commit=2
user=mysql
skip_innodb_doublewrite
innodb_buffer_pool_size = 4G
#innodb_log_buffer_size = 16M
#innodb_log_file_size = 4G
#sync_binlog = 100
innodb_write_io_threads = 8
innodb_read_io_threads = 8
#innodb_log_files_in_group = 2
max_allowed_packet = 200M

log_bin=master-bin
server-id=2
binlog-expire-logs-seconds=604800
binlog_ignore_db=mysql
binlog_ignore_db=information_schema
binlog_ignore_db=performation_schema
binlog_ignore_db=sys
binlog_format=row
replica_skip_errors=1062

#read_buffer_size=2M 
#read_rnd_buffer_size=16M
#back_log=300
#join_buffer_size=16M
#sort_buffer_size=16M
sql_mode=STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION

tmp_table_size=1G
max_heap_table_size=1G
log_timestamps=SYSTEM

# Disabling symbolic-links is recommended to prevent assorted security risks
symbolic-links=0

#log-error=/data/mysql/mysqld.log
#pid-file=/data/mysql/mysqld.pid

[client]
default-character-set=utf8mb4
#socket=/data/mysql/mysql.sock
port=3306
#user=aaa
#password=AAAvpdn135@$^

[mysql]
default-character-set=utf8mb4
#character-set-server 
