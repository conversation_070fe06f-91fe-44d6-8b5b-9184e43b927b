CREATE TABLE bras (
    bras_ip        VARCHAR(15) NOT NULL, 
    bras_secret    VARCHAR(15) NOT NULL,
    bras_model     VARCHAR(32) NOT NULL,
    bras_vendor    VARCHAR(32) NOT NULL,
    bras_area      VARCHAR(20) NOT NULL,
    bras_description    VARCHAR(1000) NOT NULL,
    PRIMARY KEY (bras_ip)   
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE user (
    user_name        VARCHAR(40) NOT NULL,
    user_domain          VARCHAR(48),
    user_password        VARCHAR(64),
    user_password_type   VARCHAR(64),    
    user_business_type            INT NOT NULL, 
    user_area  VARCHAR(20) NOT NULL,
    user_line_bind_type   INT DEFAULT NULL,
    user_line_bind_info   VARCHAR(350),
    user_bind_nas      VARCHAR(15),
    user_bind_ip        VARCHAR(64),
    user_allow_onlinenums INT DEFAULT NULL,
    user_status  INT NOT NULL,      
    user_pause_DATETIME  DATETIME  NULL,
    user_open_DATETIME   DATETIME NOT NULL,
    user_open_operator VARCHAR(100),      
    user_expire_DATETIME   DATETIME  NULL,
    user_modiffy_DATETIME   DATETIME  NULL,
    user_modiffy_operator VARCHAR(100),  
    user_down_bandwidth DECIMAL(10, 0) DEFAULT NULL,
    user_up_bandwidth DECIMAL(10, 0) DEFAULT NULL,
    user_ip_type    INT DEFAULT NULL,
    user_ipv6_prefix    VARCHAR(45),
    user_ipv6_interfaceid    VARCHAR(20), 
    user_bindwidth_template_id VARCHAR(20),
    user_product_type   INT DEFAULT NULL,
    user_allow_start_time VARCHAR(8),
    user_allow_stop_time VARCHAR(8),
    user_primary_username  VARCHAR(40),
    user_session_timemout   INT,
    PRIMARY KEY (user_name,user_domain, user_business_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE INDEX INDEX_user ON user(user_business_type, user_line_bind_type, user_bind_nas, user_line_bind_info);

CREATE TABLE authrecord (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  user_name VARCHAR(40) NOT NULL,
  user_domain VARCHAR(48),
  user_business_type  VARCHAR(20)  NULL,
  auth_date DATETIME NOT NULL,
  auth_result_code INT NOT NULL,
  bras_ip VARCHAR(15),
  bras_port INT,
  bras_port_type VARCHAR(20),
  line_info VARCHAR(350),
  mac VARCHAR(20),
  client_type INT,
  radius_server VARCHAR(20),
  dail_user_name VARCHAR(40)
);

CREATE INDEX authrecord_index ON authrecord (user_name);

CREATE TABLE onlinerecord (
    user_name VARCHAR(40) NOT NULL,
    user_domain VARCHAR(48),
    user_business_type INT NOT NULL,
    online_time DATETIME not null,
    line_info VARCHAR(350),
    mac VARCHAR(20),
    bras_ip VARCHAR(15) NOT NULL,
    bras_port INT,
    bras_port_type VARCHAR(20),
    session_id VARCHAR(64) NOT NULL ,
    user_area VARCHAR(20),
    bras_area VARCHAR(20),
    client_type INT,
    packet_process_time DATETIME NOT NULL,
    dail_user_name VARCHAR(40),
    user_nat_framedip VARCHAR(15),
    user_nat_beginport INT,
    user_nat_endport INT,
    user_framedip VARCHAR(15),
    user_framedipv6 VARCHAR(45),
    user_delegated_ipv6prefix VARCHAR(45),
    user_ipv4_outoctets BIGINT UNSIGNED,
    user_ipv4_inoctets BIGINT UNSIGNED,
    user_ipv4_outpackets BIGINT UNSIGNED,
    user_ipv4_inpackets BIGINT UNSIGNED,
    user_ipv6_outoctets BIGINT UNSIGNED,
    user_ipv6_inoctets BIGINT UNSIGNED,
    user_ipv6_outpackets BIGINT UNSIGNED,
    user_ipv6_inpackets BIGINT UNSIGNED,
    packet_type VARCHAR(15) NOT NULL,
    radius_server VARCHAR(20),
    PRIMARY KEY (session_id)
);

CREATE INDEX idx_user_nat_framedip ON onlinerecord (user_nat_framedip, user_nat_beginport, user_nat_endport);
CREATE INDEX idx_user_framedipv6 ON onlinerecord (user_framedipv6, user_delegated_ipv6prefix);
CREATE UNIQUE INDEX idx_unique_user_bras_ip_session_id ON onlinerecord (user_name, bras_ip, session_id);
CREATE INDEX idx_user_framedip ON onlinerecord (user_framedip);
CREATE INDEX idx_mac ON onlinerecord (mac);
CREATE INDEX idx_packet_process_time ON onlinerecord (packet_process_time);
CREATE INDEX idx_user_business_type_user_name ON onlinerecord (user_name,user_domain,user_business_type);

CREATE TABLE detail (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(40) NOT NULL,
    user_domain VARCHAR(48),
    user_business_type INT NOT NULL,
    online_time DATETIME NOT NULL,
    offline_time DATETIME NOT NULL,
    duration INT NOT NULL,
    user_nat_framedip VARCHAR(15),
    user_nat_beginport INT,
    user_nat_endport INT,
    user_framedip VARCHAR(15),
    user_framedipv6 VARCHAR(45),
    user_delegated_ipv6prefix VARCHAR(45),   
    user_ipv4_outoctets BIGINT UNSIGNED,
    user_ipv4_inoctets BIGINT UNSIGNED,
    user_ipv4_outpackets BIGINT UNSIGNED,
    user_ipv4_inpackets BIGINT UNSIGNED,
    user_ipv6_outoctets BIGINT UNSIGNED,
    user_ipv6_inoctets BIGINT UNSIGNED,
    user_ipv6_outpackets BIGINT UNSIGNED,
    user_ipv6_inpackets BIGINT UNSIGNED,
    line_info VARCHAR(350),
    mac VARCHAR(20),
    bras_ip VARCHAR(15) NOT NULL,
    bras_port INT,
    bras_port_type VARCHAR(20),
    session_id VARCHAR(64) NOT NULL,
    user_area VARCHAR(20),
    bras_area VARCHAR(20),
    client_type INT,
    packet_process_time DATETIME NOT NULL,
    dail_user_name VARCHAR(40),
    down_reason VARCHAR(40),
    radius_server VARCHAR(20)
);

CREATE INDEX idx_user_business_type_process_time ON detail (user_business_type, packet_process_time);
CREATE INDEX idx_user_nat_framedip_ports ON detail (user_nat_framedip, user_nat_beginport, user_nat_endport);
CREATE INDEX idx_user_framedipv6_prefix ON detail (user_framedipv6, user_delegated_ipv6prefix);
CREATE INDEX idx_online_time_offline_time ON detail (online_time, offline_time);
CREATE INDEX idx_mac ON detail (mac);
CREATE INDEX idx_user_name_business_type ON detail (user_name, user_business_type);
CREATE INDEX idx_user_framedip ON detail (user_framedip);
commit;
