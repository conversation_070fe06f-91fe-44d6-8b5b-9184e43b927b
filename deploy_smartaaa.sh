#!/bin/bash

# SmartAAA 完整部署脚本
# 包含web前后端和radius AAA服务的一键部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 打印函数
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# 显示帮助
show_help() {
    echo "SmartAAA 完整部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  build     构建所有服务镜像"
    echo "  deploy    部署所有服务"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  clean     清理所有资源"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV     环境 (dev|test|prod，默认: prod)"
    echo "  -v, --verbose     显示详细信息"
    echo "  -h, --help        显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 build -e prod"
    echo "  $0 deploy --env test"
    echo "  $0 logs radius-aaa"
}

# 默认参数
COMMAND=""
ENV="prod"
VERBOSE=false
SERVICE=""

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        build|deploy|start|stop|restart|status|logs|clean)
            COMMAND="$1"
            shift
            ;;
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            if [[ -z "$SERVICE" && "$COMMAND" == "logs" ]]; then
                SERVICE="$1"
            else
                print_error "未知参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查命令
if [[ -z "$COMMAND" ]]; then
    print_error "必须指定命令"
    show_help
    exit 1
fi

# 验证环境
case $ENV in
    dev|test|prod) ;;
    *)
        print_error "不支持的环境: $ENV"
        exit 1
        ;;
esac

# 配置文件
COMPOSE_FILE="docker-compose-integrated.yml"

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装"
        exit 1
    fi

    # 检查Docker Compose (优先使用新版本的 docker compose)
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        print_info "使用Docker Compose V2: $(docker compose version --short)"
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        print_info "使用Docker Compose V1: $(docker-compose --version)"
    else
        print_error "Docker Compose未安装"
        print_info "请安装Docker Compose或确保Docker版本支持 'docker compose' 命令"
        exit 1
    fi
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "aaa-net"; then
        print_step "创建Docker网络: aaa-net"
        docker network create --driver bridge \
            --subnet=**********/16 \
            --ip-range=**********/24 \
            aaa-net
        print_success "网络创建成功"
    else
        print_info "网络 aaa-net 已存在"
    fi
}

# 构建服务
build_services() {
    print_step "构建所有服务镜像..."
    
    # 构建radius AAA服务
    print_info "构建RADIUS AAA服务..."
    cd radius
    chmod +x build_secure.sh
    ./build_secure.sh -t latest -e "$ENV" $([ "$VERBOSE" == "true" ] && echo "-v")
    cd ..
    
    # 构建web服务
    print_info "构建Web服务..."
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" build
    
    print_success "所有服务构建完成"
}

# 部署服务
deploy_services() {
    print_step "部署SmartAAA服务..."
    
    create_network
    
    # 启动服务
    print_info "启动所有服务..."
    $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services_health
    
    print_success "SmartAAA部署完成"
}

# 检查服务健康状态
check_services_health() {
    print_step "检查服务健康状态..."
    
    local services=("ynyb-web" "ynyb-backend" "radius-aaa-secure")
    
    for service in "${services[@]}"; do
        if docker ps | grep -q "$service"; then
            print_success "$service: 运行中"
        else
            print_error "$service: 未运行"
        fi
    done
}

# 执行命令
case $COMMAND in
    "build")
        check_docker
        build_services
        ;;
    "deploy")
        check_docker
        build_services
        deploy_services
        ;;
    "start")
        check_docker
        create_network
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" up -d
        print_success "服务已启动"
        ;;
    "stop")
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down
        print_success "服务已停止"
        ;;
    "restart")
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" restart
        print_success "服务已重启"
        ;;
    "status")
        check_services_health
        $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" ps
        ;;
    "logs")
        if [[ -n "$SERVICE" ]]; then
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" logs -f "$SERVICE"
        else
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" logs -f
        fi
        ;;
    "clean")
        print_warning "这将删除所有容器、镜像和数据，确定继续吗？(y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            $DOCKER_COMPOSE_CMD -f "$COMPOSE_FILE" down -v --rmi all
            docker system prune -af
            print_success "清理完成"
        else
            print_info "取消清理"
        fi
        ;;
esac
