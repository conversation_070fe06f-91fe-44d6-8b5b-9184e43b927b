# 集成的docker-compose配置
# 包含web前端、web后端和radius AAA服务

services:
  # Web前端服务
  ynyb-web:
    container_name: ynyb-web
    ports:
      - "8080:8080"
    build:
      context: ./web
      dockerfile: ./docker_env/web/Dockerfile
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./web/docker_env/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./web/docker_env/nginx/my.conf:/etc/nginx/conf.d/default.conf
      - ./web/backend/media:/backend/media
      - ./web/web:/web
    expose:
      - "8080"
    restart: always
    networks:
      aaa-net:
        ipv4_address: **********
    depends_on:
      - ynyb-backend-nuitka

  # Web后端服务（Nuitka编译版本）
  ynyb-backend-nuitka:
    image: ynyb-backend-nuitka:latest
    container_name: ynyb-backend-nuitka
    working_dir: /backend
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_HOST: ynyb-mysql
      TZ: Asia/Shanghai
    volumes:
      # 日志文件可写挂载
      - ./web/logs:/backend/logs
      # 媒体文件可写挂载
      - ./web/backend/media:/backend/media
      # 配置文件只读挂载（如果需要动态配置）
      - ./web/backend/conf:/backend/conf:ro
    ports:
      - "8000:8000"
    expose:
      - "8000"
    restart: always
    networks:
      aaa-net:
        ipv4_address: **********
    # 安全配置（为Nuitka onefile执行调整）
    security_opt:
      - no-new-privileges:true
    tmpfs:
      - /tmp:exec,nosuid,size=2g
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RADIUS AAA服务
  radius-aaa:
    image: radius-aaa-secure:latest
    container_name: radius-aaa-secure
    ports:
      - "1812:1812/udp"  # RADIUS Authentication
      - "1813:1813/udp"  # RADIUS Accounting
      - "3799:3799/udp"  # RADIUS CoA (Change of Authorization)
    environment:
      - TZ=Asia/Shanghai
      - RADIUS_ENV=production
      - DATABASE_HOST=ynyb-mysql
    volumes:
      # 配置文件只读挂载
      - ./radius/conf:/app/conf:ro
      # 日志文件可写挂载
      - ./radius/logs:/app/logs
    networks:
      aaa-net:
        ipv4_address: **********
    restart: always
    # 安全配置（为Nuitka onefile执行调整）
    security_opt:
      - no-new-privileges:true
    tmpfs:
      - /tmp:exec,nosuid,size=100m
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    # 健康检查
    healthcheck:
      test: ["CMD", "/app/radius_server", "--health-check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


  # 数据库服务（可选，如果使用外部数据库则注释掉）
  # ynyb-mysql:
  #   image: mysql:8.0
  #   container_name: ynyb-mysql
  #   environment:
  #     MYSQL_ROOT_PASSWORD: your_root_password
  #     MYSQL_DATABASE: smartaaa
  #     MYSQL_USER: aaa_user
  #     MYSQL_PASSWORD: your_password
  #     TZ: Asia/Shanghai
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./database/mysql:/docker-entrypoint-initdb.d
  #   ports:
  #     - "3306:3306"
  #   networks:
  #     aaa-net:
  #       ipv4_address: **********
  #   restart: always
  #   command: --default-authentication-plugin=mysql_native_password

networks:
  aaa-net:
    external: true
    name: aaa-net

# volumes:
#   mysql_data:
#     driver: local
