# 认证记录入库增强功能

## 概述

本次修改为认证入库功能增加了 `session_id` 和 `user_area` 字段的支持，实现了以下需求：

1. **session_id**: 从RADIUS包的 `Acct-Session-Id` 属性获取并入库
2. **user_area**: 根据用户存在性动态设置区域信息
   - 如果认证的用户存在，则使用 `user.user_area`
   - 如果认证的用户不存在，则使用 `bras.bras_area`

## 修改的文件

### 1. `radius/aaaserver/async_dbutil.py`

#### 修改内容：
- **更新 `insert_authrecord` 函数**：在SQL插入语句中增加了 `session_id` 和 `user_area` 字段
- **新增 `get_bras_area_by_ip` 函数**：根据BRAS IP地址查询BRAS区域信息

#### 关键代码：
```python
# 更新的SQL插入语句
sql = """
    INSERT INTO authrecord (
        user_name, user_domain, user_business_type, auth_date,
        auth_result_code, bras_ip, bras_port, bras_port_type, line_info,
        mac, radius_server, dail_user_name, service_type, framed_protocol,
        framed_ip_netmask, session_timeout, acct_interim_interval, framed_ip_address,
        user_down_bandwidth, user_up_bandwidth, session_id, user_area
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
"""

# 新增的BRAS区域查询函数
@async_db_operation
async def get_bras_area_by_ip(conn, bras_ip):
    """异步根据BRAS IP获取BRAS区域"""
    async with conn.cursor() as cursor:
        await cursor.execute("SELECT bras_area FROM bras WHERE bras_ip = %s", (bras_ip,))
        result = await cursor.fetchone()
        return result[0] if result else None
```

### 2. `radius/aaaserver/async_server.py`

#### 修改内容：
- **更新认证记录初始化**：在 `record` 字典中添加了 `session_id` 字段的获取
- **新增 `_set_user_area_for_auth_record` 方法**：实现用户区域的动态设置逻辑
- **更新认证流程**：在记录入库前调用区域设置方法

#### 关键代码：
```python
# 认证记录初始化时添加session_id
record = {
    'bras_ip': auth_attrs.get('NAS-IP-Address'),
    'bras_port': bras_port,
    'bras_port_type': auth_attrs.get('NAS-Port-Type'),
    'line_info': auth_attrs.get('NAS-Port-Id'),
    'mac': validated_mac,
    'radius_server': self.server.hostname,
    'auth_date': now,
    'session_id': auth_attrs.get('Acct-Session-Id')  # 新增：会话ID
}

# 新增的用户区域设置方法
async def _set_user_area_for_auth_record(self, record, user_info):
    """设置认证记录的user_area字段"""
    try:
        if user_info and user_info.get('user_area'):
            # 如果认证的用户存在，使用user.user_area
            record['user_area'] = user_info['user_area']
            log_info(f"Set user_area from user info: {record['user_area']}")
        else:
            # 如果认证的用户不存在，使用bras.bras_area
            bras_ip = record.get('bras_ip')
            if bras_ip:
                bras_area = await async_dbutil.get_bras_area_by_ip(bras_ip)
                if bras_area:
                    record['user_area'] = bras_area
                    log_info(f"Set user_area from BRAS info: {record['user_area']}")
                else:
                    record['user_area'] = ''
                    log_warning(f"BRAS area not found for IP: {bras_ip}")
            else:
                record['user_area'] = ''
                log_warning("No BRAS IP found in record")
    except Exception as e:
        log_error(f"Error setting user_area for auth record: {e}")
        record['user_area'] = ''  # 设置默认值
```

## 数据库表结构

### authrecord 表
该表已经包含了所需的字段：
- `session_id` (varchar(64)): 会话ID
- `user_area` (varchar(20)): 用户区域

### 相关表
- `user` 表：包含 `user_area` 字段，存储用户的区域信息
- `bras` 表：包含 `bras_area` 字段，存储BRAS设备的区域信息

## 功能逻辑

### 认证成功场景
1. 从RADIUS包中提取 `Acct-Session-Id` 作为 `session_id`
2. 查询用户信息，如果用户存在且有 `user_area`，则使用用户的区域
3. 将认证记录（包含session_id和user_area）插入数据库

### 认证失败场景
1. 从RADIUS包中提取 `Acct-Session-Id` 作为 `session_id`
2. 由于用户不存在或认证失败，查询BRAS设备的区域信息
3. 使用BRAS的 `bras_area` 作为 `user_area`
4. 将认证记录插入数据库

## 测试

创建了测试脚本 `radius/test_auth_record.py` 用于验证功能：
- 测试BRAS区域查询功能
- 测试认证记录插入功能
- 验证session_id和user_area字段的正确设置

## 日志记录

增强了日志记录功能：
- 记录用户区域设置的来源（用户信息或BRAS信息）
- 记录BRAS区域查询的结果
- 记录认证记录插入的状态

## 兼容性

- 保持了与现有代码的完全兼容性
- 新增字段有默认值处理，不会影响现有功能
- 错误处理机制确保即使区域查询失败也不会影响认证流程
