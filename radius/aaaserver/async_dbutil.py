"""
异步数据库工具模块
支持协程和连接池
"""

import asyncio
import logging
import configparser
from functools import wraps
from urllib.parse import quote_plus
import aiomysql
from contextlib import asynccontextmanager

# 配置日志 - 使用accounting logger，级别为debug
logger = logging.getLogger('accounting')

# 读取配置
db_config = configparser.ConfigParser()
db_config.read('conf/db.ini')

# 数据库连接池
_pool = None

async def init_db_pool():
    """初始化数据库连接池"""
    global _pool
    
    if _pool is not None:
        return _pool
    
    try:
        # 构建数据库连接参数
        user = db_config.get('mysql', 'user')
        password = db_config.get('mysql', 'password')
        host = db_config.get('mysql', 'host')
        database = db_config.get('mysql', 'database')
        pool_size = db_config.getint('mysql', 'pool_size', fallback=20)
        
        _pool = await aiomysql.create_pool(
            host=host,
            user=user,
            password=password,
            db=database,
            minsize=5,
            maxsize=pool_size,
            autocommit=True,
            charset='utf8mb4'
        )
        
        logger.info(f"Database pool created successfully with {pool_size} connections")
        return _pool
        
    except Exception as e:
        logger.error(f"Error creating database pool: {e}")
        raise

async def close_db_pool():
    """关闭数据库连接池"""
    global _pool
    if _pool:
        _pool.close()
        await _pool.wait_closed()
        _pool = None
        logger.debug("Database pool closed")

@asynccontextmanager
async def get_db_connection():
    """获取数据库连接的异步上下文管理器"""
    if _pool is None:
        await init_db_pool()
    
    async with _pool.acquire() as conn:
        try:
            yield conn
        except Exception as e:
            logger.error(f"Database operation error: {e}")
            raise

def async_db_operation(func):
    """异步数据库操作装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            async with get_db_connection() as conn:
                return await func(conn, *args, **kwargs)
        except Exception as e:
            logger.error(f"Database error in {func.__name__}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    return wrapper

@async_db_operation
async def get_bras_list(conn):
    """异步获取所有BRAS设备列表"""
    async with conn.cursor(aiomysql.DictCursor) as cursor:
        await cursor.execute(
            "SELECT bras_ip, bras_secret, bras_model, bras_vendor, bras_area FROM bras"
        )
        result = await cursor.fetchall()
        return list(result)

@async_db_operation
async def get_bras_by_ip(conn, ip):
    """异步根据IP获取BRAS设备信息"""
    async with conn.cursor(aiomysql.DictCursor) as cursor:
        await cursor.execute("""
            SELECT bras_ip, bras_secret, bras_model, bras_vendor, bras_area
            FROM bras WHERE bras_ip = %s
        """, (ip,))
        result = await cursor.fetchone()
        return result

@async_db_operation
async def get_user_info(conn, user_name, user_domain=None):
    """异步获取用户信息"""
    async with conn.cursor(aiomysql.DictCursor) as cursor:
        if user_domain:
            sql = """
                SELECT user_name, user_password, user_password_type, user_domain,
                       user_business_type, user_area, user_line_bind_type, user_line_bind_info,
                       user_bind_nas, user_bind_ip, user_allow_onlinenums, user_status,
                       user_pause_datetime, user_open_datetime, user_open_operator,
                       user_expire_datetime, user_modify_datetime, user_modify_operator,
                       user_down_bandwidth, user_up_bandwidth, user_ip_type,
                       user_ipv6_prefix, user_ipv6_interfaceid, user_bindwidth_template_id,
                       user_product_type, user_allow_start_time, user_allow_stop_time,
                       user_primary_username, user_session_timemout
                FROM user WHERE user_name = %s AND user_domain = %s
            """
            await cursor.execute(sql, (user_name, user_domain))
        else:
            sql = """
                SELECT user_name, user_password, user_password_type, user_domain,
                       user_business_type, user_area, user_line_bind_type, user_line_bind_info,
                       user_bind_nas, user_bind_ip, user_allow_onlinenums, user_status,
                       user_pause_datetime, user_open_datetime, user_open_operator,
                       user_expire_datetime, user_modify_datetime, user_modify_operator,
                       user_down_bandwidth, user_up_bandwidth, user_ip_type,
                       user_ipv6_prefix, user_ipv6_interfaceid, user_bindwidth_template_id,
                       user_product_type, user_allow_start_time, user_allow_stop_time,
                       user_primary_username, user_session_timemout
                FROM user WHERE user_name = %s AND user_domain = ''
            """
            await cursor.execute(sql, (user_name,))
        
        result = await cursor.fetchone()
        return result

@async_db_operation
async def get_vpdn_list(conn):
    """异步获取所有VPDN域名列表"""
    async with conn.cursor(aiomysql.DictCursor) as cursor:
        await cursor.execute(
            "SELECT vpdn_domain, vpdn_name, vpdn_areano, description FROM vpdn"
        )
        result = await cursor.fetchall()
        return list(result)

@async_db_operation
async def check_domain_exists(conn, domain):
    """异步检查域名是否存在于VPDN表中"""
    async with conn.cursor() as cursor:
        await cursor.execute("SELECT COUNT(*) FROM vpdn WHERE vpdn_domain = %s", (domain,))
        result = await cursor.fetchone()
        return result[0] > 0 if result else False

@async_db_operation
async def insert_authrecord(conn, record):
    """异步插入认证记录"""
    async with conn.cursor() as cursor:
        sql = """
            INSERT INTO authrecord (
                user_name, user_domain, user_business_type, auth_date,
                auth_result_code, bras_ip, bras_port, bras_port_type, line_info,
                mac, radius_server, dail_user_name, service_type, framed_protocol,
                framed_ip_netmask, session_timeout, acct_interim_interval, framed_ip_address,
                user_down_bandwidth, user_up_bandwidth
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """

        # 格式化日期时间
        auth_date = record['auth_date']
        if hasattr(auth_date, 'strftime'):
            auth_date = auth_date.strftime('%Y-%m-%d %H:%M:%S')

        values = (
            record['user_name'], record['user_domain'], record['user_business_type'],
            auth_date, record['auth_result_code'], record['bras_ip'],
            record['bras_port'], record['bras_port_type'], record['line_info'],
            record['mac'], record['radius_server'], record['dail_user_name'],
            record.get('service_type'), record.get('framed_protocol'),
            record.get('framed_ip_netmask'), record.get('session_timeout'),
            record.get('acct_interim_interval'), record.get('framed_ip_address'),
            record.get('user_down_bandwidth'), record.get('user_up_bandwidth')
        )

        await cursor.execute(sql, values)
        logger.debug("Authentication result inserted into authrecord table")
        return cursor.rowcount > 0

@async_db_operation
async def get_online_count_by_user(conn, user_name, user_domain, user_business_type):
    """异步获取用户在线数量"""
    async with conn.cursor() as cursor:
        await cursor.execute("""
            SELECT COUNT(*) FROM onlinerecord
            WHERE user_name = %s AND user_domain = %s AND user_business_type = %s
        """, (user_name, user_domain, user_business_type))
        result = await cursor.fetchone()
        return result[0] if result else 0

@async_db_operation
async def insert_onlinerecord(conn, record):
    """异步插入在线记录"""
    async with conn.cursor() as cursor:
        sql = """
            INSERT INTO onlinerecord (
                user_name, user_domain, user_business_type, online_time, line_info,
                mac, bras_ip, bras_port, bras_port_type, session_id, user_area,
                bras_area, client_type, packet_process_time, dail_user_name,
                user_nat_framedip, user_nat_beginport, user_nat_endport,
                user_framedip, user_framedipv6, user_delegated_ipv6prefix,
                user_ipv4_outoctets, user_ipv4_inoctets, user_ipv4_outpackets,
                user_ipv4_inpackets, user_ipv6_outoctets, user_ipv6_inoctets,
                user_ipv6_outpackets, user_ipv6_inpackets, packet_type, radius_server
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """
        
        # 格式化日期时间
        online_time = record['online_time']
        if hasattr(online_time, 'strftime'):
            online_time = online_time.strftime('%Y-%m-%d %H:%M:%S')
            
        packet_process_time = record['packet_process_time']
        if hasattr(packet_process_time, 'strftime'):
            packet_process_time = packet_process_time.strftime('%Y-%m-%d %H:%M:%S')
        
        values = (
            record.get('user_name', ''), record.get('user_domain', ''), record.get('user_business_type', 0),
            online_time, record.get('line_info', ''), record.get('mac', ''), record.get('bras_ip', ''),
            record.get('bras_port', 0), record.get('bras_port_type', ''), record.get('session_id', ''),
            record.get('user_area', ''), record.get('bras_area', 0), record.get('client_type', 0),
            packet_process_time, record.get('dail_user_name', ''),
            record.get('user_nat_framedip', ''), record.get('user_nat_beginport', 0),
            record.get('user_nat_endport', 0), record.get('user_framedip', ''),
            record.get('user_framedipv6', ''), record.get('user_delegated_ipv6prefix', ''),
            record.get('user_ipv4_outoctets', 0), record.get('user_ipv4_inoctets', 0),
            record.get('user_ipv4_outpackets', 0), record.get('user_ipv4_inpackets', 0),
            record.get('user_ipv6_outoctets', 0), record.get('user_ipv6_inoctets', 0),
            record.get('user_ipv6_outpackets', 0), record.get('user_ipv6_inpackets', 0),
            record.get('packet_type', ''), record.get('radius_server', '')
        )
        
        await cursor.execute(sql, values)
        logger.debug("Online record inserted into onlinerecord table")
        return cursor.rowcount > 0

@async_db_operation
async def get_online_session_by_id(conn, session_id):
    """异步根据会话ID获取在线记录"""
    async with conn.cursor(aiomysql.DictCursor) as cursor:
        await cursor.execute("""
            SELECT user_name, user_domain, user_business_type, bras_ip,
                   session_id, user_framedip, online_time
            FROM onlinerecord WHERE session_id = %s
        """, (session_id,))
        result = await cursor.fetchone()
        return result

@async_db_operation
async def get_online_sessions_by_user(conn, user_name, user_domain=None):
    """异步根据用户名获取在线会话"""
    async with conn.cursor(aiomysql.DictCursor) as cursor:
        if user_domain:
            await cursor.execute("""
                SELECT user_name, user_domain, user_business_type, bras_ip,
                       session_id, user_framedip, online_time
                FROM onlinerecord
                WHERE user_name = %s AND user_domain = %s
            """, (user_name, user_domain))
        else:
            await cursor.execute("""
                SELECT user_name, user_domain, user_business_type, bras_ip,
                       session_id, user_framedip, online_time
                FROM onlinerecord
                WHERE user_name = %s AND user_domain = ''
            """, (user_name,))
        result = await cursor.fetchall()
        return list(result)

@async_db_operation
async def insert_coa_record(conn, record):
    """异步插入CoA操作记录"""
    async with conn.cursor() as cursor:
        sql = """
            INSERT INTO coa_record (
                session_id, user_name, user_domain, bras_ip, coa_type,
                coa_result, coa_time, radius_server, client_ip, request_attributes
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """

        # 格式化日期时间
        coa_time = record['coa_time']
        if hasattr(coa_time, 'strftime'):
            coa_time = coa_time.strftime('%Y-%m-%d %H:%M:%S')

        values = (
            record.get('session_id', ''), record.get('user_name', ''),
            record.get('user_domain', ''), record.get('bras_ip', ''),
            record.get('coa_type', ''), record.get('coa_result', ''),
            coa_time, record.get('radius_server', ''),
            record.get('client_ip', ''), record.get('request_attributes', '')
        )

        try:
            await cursor.execute(sql, values)
            logger.debug("CoA record inserted into coa_record table")
            return cursor.rowcount > 0
        except Exception as e:
            # 如果表不存在，记录警告但不抛出异常
            logger.warning(f"Failed to insert CoA record (table may not exist): {e}")
            return False

# 同步版本的函数（用于向后兼容）
def get_bras_list_sync():
    """同步版本的get_bras_list，用于在非异步环境中调用"""
    import dbutil
    return dbutil.get_bras_list()

def get_vpdn_list_sync():
    """同步版本的get_vpdn_list，用于在非异步环境中调用"""
    import dbutil
    return dbutil.get_vpdn_list()

@async_db_operation
async def update_onlinerecord_by_sessionid(conn, session_id, update_record, insert_record, acct_update_flag):
    """异步根据session_id更新在线记录"""
    async with conn.cursor() as cursor:
        # 检查记录是否存在
        await cursor.execute("SELECT COUNT(session_id) FROM onlinerecord WHERE session_id = %s", (session_id,))
        result = await cursor.fetchone()
        record_exists = result[0] > 0

        if record_exists:
            # 构建更新查询
            update_fields = []
            params = []

            for field, value in update_record.items():
                update_fields.append(f"{field} = %s")
                params.append(value)

            params.append(session_id)  # 添加WHERE条件的参数

            if update_fields:
                update_query = f"UPDATE onlinerecord SET {', '.join(update_fields)} WHERE session_id = %s"
                logger.debug(f"Executing update query: {update_query}")
                logger.debug(f"Update parameters: {params}")
                await cursor.execute(update_query, params)

                if cursor.rowcount > 0:
                    logger.debug(f"Record updated in onlinerecord table, affected rows: {cursor.rowcount}")
                else:
                    logger.debug("No record found for the given session_id")

                return cursor.rowcount > 0
            else:
                logger.warning("No update fields provided, skipping update")
                return False
        else:
            if not acct_update_flag or (acct_update_flag and insert_record.get('duration', 0) > 5):
                # 如果不存在记录，则插入数据到onlinerecord表
                logger.debug(f"No record found for session_id {session_id}, inserting new record into onlinerecord.")
                return await insert_onlinerecord(insert_record)
            else:
                logger.debug(f"No record found for session_id {session_id}, but address update packet before offline do not insert into onlinerecord table")
                return False

@async_db_operation
async def insert_detail(conn, record):
    """异步插入详细记录"""
    async with conn.cursor() as cursor:
        sql = """
            INSERT INTO detail (
                user_name, user_domain, user_business_type, online_time, offline_time,
                duration, user_nat_framedip, user_nat_beginport, user_nat_endport,
                user_framedip, user_framedipv6, user_delegated_ipv6prefix, user_ipv4_outoctets,
                user_ipv4_inoctets, user_ipv4_outpackets, user_ipv4_inpackets, user_ipv6_outoctets,
                user_ipv6_inoctets, user_ipv6_outpackets, user_ipv6_inpackets, line_info,
                mac, bras_ip, bras_port, bras_port_type, session_id, user_area,
                bras_area, client_type, packet_process_time, dail_user_name, down_reason,
                radius_server
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """

        # 格式化日期时间
        if isinstance(record['online_time'], str):
            online_time = record['online_time']
        else:
            online_time = record['online_time'].strftime('%Y-%m-%d %H:%M:%S')

        if isinstance(record['offline_time'], str):
            offline_time = record['offline_time']
        else:
            offline_time = record['offline_time'].strftime('%Y-%m-%d %H:%M:%S')

        if isinstance(record['packet_process_time'], str):
            packet_process_time = record['packet_process_time']
        else:
            packet_process_time = record['packet_process_time'].strftime('%Y-%m-%d %H:%M:%S')

        values = (
            record['user_name'], record['user_domain'], record['user_business_type'],
            online_time, offline_time, record['duration'],
            record.get('user_nat_framedip', ''), record.get('user_nat_beginport', 0),
            record.get('user_nat_endport', 0), record.get('user_framedip', ''),
            record.get('user_framedipv6', ''), record.get('user_delegated_ipv6prefix', ''),
            record.get('user_ipv4_outoctets', 0), record.get('user_ipv4_inoctets', 0),
            record.get('user_ipv4_outpackets', 0), record.get('user_ipv4_inpackets', 0),
            record.get('user_ipv6_outoctets', 0), record.get('user_ipv6_inoctets', 0),
            record.get('user_ipv6_outpackets', 0), record.get('user_ipv6_inpackets', 0),
            record.get('line_info', ''), record.get('mac', ''), record.get('bras_ip', ''),
            record.get('bras_port', 0), record.get('bras_port_type', ''),
            record.get('session_id', ''), record.get('user_area', ''),
            record.get('bras_area', 0), record.get('client_type', 0),
            packet_process_time, record.get('dail_user_name', ''),
            record.get('down_reason', 0), record['radius_server']
        )

        await cursor.execute(sql, values)
        logger.debug("Detail record inserted into detail table")
        return cursor.rowcount > 0

@async_db_operation
async def delete_onlinerecord_by_sessionid(conn, session_id):
    """异步根据session_id删除在线记录"""
    async with conn.cursor() as cursor:
        await cursor.execute("DELETE FROM onlinerecord WHERE session_id = %s", (session_id,))

        if cursor.rowcount > 0:
            logger.debug(f"Record with session_id {session_id} deleted from onlinerecord table")
        else:
            logger.debug(f"No record found for the given session_id {session_id}")

        return cursor.rowcount > 0

@async_db_operation
async def get_bras_by_ip(conn, bras_ip):
    """异步根据IP获取BRAS信息"""
    async with conn.cursor() as cursor:
        await cursor.execute("SELECT bras_ip, bras_vendor, bras_model, bras_area FROM bras WHERE bras_ip = %s", (bras_ip,))
        result = await cursor.fetchone()

        if result:
            return {
                'bras_ip': result[0],
                'bras_vendor': result[1],
                'bras_model': result[2],
                'bras_area': result[3]
            }
        else:
            return None
