"""
多日志文件系统
支持不同类型的日志分别记录到不同文件
配置从 conf/logging.conf 读取
"""

import logging
import logging.config
import os
from datetime import datetime
import pytz
from contextvars import ContextVar
from typing import Dict, Any, Optional

# 上下文变量
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
session_info_var: ContextVar[Optional[Dict[str, Any]]] = ContextVar('session_info', default=None)

class ContextFilter(logging.Filter):
    """上下文过滤器，为日志记录添加上下文信息"""

    def filter(self, record):
        # 获取上下文信息
        req_id = request_id_var.get() or '----'
        session_info = session_info_var.get() or {}

        # 添加到日志记录中
        record.request_id = req_id
        record.user_name = session_info.get('user_name', '----')
        record.client_ip = session_info.get('client_ip', '----')
        record.request_type = session_info.get('request_type', '----')

        return True

class MultiLoggerSystem:
    """多日志文件系统"""

    def __init__(self):
        self.loggers = {}
        self.setup_loggers()

    def setup_loggers(self):
        """从配置文件设置日志器"""

        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)

        try:
            # 从配置文件加载日志配置
            logging.config.fileConfig('conf/logging.conf', disable_existing_loggers=False)

            # 获取各种类型的日志器
            self.loggers['system'] = logging.getLogger('system')
            self.loggers['authentication'] = logging.getLogger('authentication')
            self.loggers['accounting'] = logging.getLogger('accounting')
            self.loggers['coa'] = logging.getLogger('coa')

            # 为需要上下文信息的日志器添加过滤器
            context_filter = ContextFilter()
            self.loggers['authentication'].addFilter(context_filter)
            self.loggers['accounting'].addFilter(context_filter)
            self.loggers['coa'].addFilter(context_filter)

        except Exception as e:
            # 如果配置文件加载失败，使用默认配置
            print(f"Warning: Failed to load logging config from conf/logging.conf: {e}")
            self._setup_default_loggers()

    def _setup_default_loggers(self):
        """设置默认日志器（当配置文件加载失败时使用）"""

        # 1. 系统日志
        system_logger = logging.getLogger('system')
        system_logger.setLevel(logging.INFO)
        system_handler = logging.handlers.TimedRotatingFileHandler(
            'logs/system.log',
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        system_formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] %(message)s'
        )
        system_handler.setFormatter(system_formatter)
        system_logger.addHandler(system_handler)
        self.loggers['system'] = system_logger

        # 2. 认证日志
        auth_logger = logging.getLogger('authentication')
        auth_logger.setLevel(logging.INFO)
        auth_handler = logging.handlers.TimedRotatingFileHandler(
            'logs/authentication.log',
            when='H',
            interval=1,
            backupCount=168,  # 保留一周
            encoding='utf-8'
        )
        auth_formatter = logging.Formatter(
            '[%(asctime)s.%(msecs)03d] [%(levelname)s] [%(request_id)s][%(user_name)s][%(client_ip)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        auth_handler.setFormatter(auth_formatter)
        auth_logger.addHandler(auth_handler)
        auth_logger.addFilter(ContextFilter())
        self.loggers['authentication'] = auth_logger

        # 3. 计费日志
        acct_logger = logging.getLogger('accounting')
        acct_logger.setLevel(logging.INFO)
        acct_handler = logging.handlers.TimedRotatingFileHandler(
            'logs/accounting.log',
            when='H',
            interval=1,
            backupCount=168,
            encoding='utf-8'
        )
        acct_handler.setFormatter(auth_formatter)
        acct_logger.addHandler(acct_handler)
        acct_logger.addFilter(ContextFilter())
        self.loggers['accounting'] = acct_logger

        # 4. COA日志
        coa_logger = logging.getLogger('coa')
        coa_logger.setLevel(logging.INFO)
        coa_handler = logging.handlers.TimedRotatingFileHandler(
            'logs/coa.log',
            when='H',
            interval=1,
            backupCount=168,
            encoding='utf-8'
        )
        coa_handler.setFormatter(auth_formatter)
        coa_logger.addHandler(coa_handler)
        coa_logger.addFilter(ContextFilter())
        self.loggers['coa'] = coa_logger

    def get_logger(self, logger_type: str):
        """获取指定类型的日志器"""
        return self.loggers.get(logger_type)

    def log_system_error(self, message: str, exc_info=None):
        """记录系统错误"""
        self.loggers['system'].error(message, exc_info=exc_info)

    def log_system_info(self, message: str):
        """记录系统信息"""
        self.loggers['system'].info(message)

    def log_authentication(self, level: int, message: str, extra: Dict[str, Any] = None):
        """记录认证日志"""
        self._log_with_context('authentication', level, message, extra)

    def log_accounting(self, level: int, message: str, extra: Dict[str, Any] = None):
        """记录计费日志"""
        self._log_with_context('accounting', level, message, extra)

    def log_coa(self, level: int, message: str, extra: Dict[str, Any] = None):
        """记录COA日志"""
        self._log_with_context('coa', level, message, extra)

    def _log_with_context(self, logger_type: str, level: int, message: str, extra: Dict[str, Any] = None):
        """带上下文信息的日志记录"""
        logger = self.loggers[logger_type]

        # 获取当前上下文信息
        req_id = request_id_var.get()
        session_info = session_info_var.get()

        # 构建额外信息
        log_extra = {
            'request_id': req_id,
        }

        if session_info:
            log_extra.update({
                'client_ip': session_info.get('client_ip'),
                'user_name': session_info.get('user_name'),
                'request_type': session_info.get('request_type')
            })

        if extra:
            log_extra.update(extra)

        logger.log(level, message, extra=log_extra)





# 全局多日志系统实例
multi_logger = MultiLoggerSystem()

# 便捷函数
def start_request(client_ip: str, user_name: str = None, request_type: str = 'auth') -> str:
    """开始请求追踪"""
    import uuid
    req_id = str(uuid.uuid4())[:8]
    request_id_var.set(req_id)
    session_info_var.set({
        'client_ip': client_ip,
        'user_name': user_name or '----',
        'request_type': request_type,
        'start_time': datetime.now(pytz.timezone('Asia/Shanghai'))
    })
    return req_id

def end_request(result: str = 'completed'):
    """结束请求追踪"""
    # 清理上下文
    request_id_var.set(None)
    session_info_var.set(None)
    # result 参数保留用于将来可能的日志记录需求
    _ = result
    # result 参数保留用于将来可能的日志记录需求

# 认证日志函数
def auth_info(message: str, extra: Dict[str, Any] = None):
    """认证信息日志"""
    multi_logger.log_authentication(logging.INFO, message, extra)

def auth_debug(message: str, extra: Dict[str, Any] = None):
    """认证调试日志"""
    multi_logger.log_authentication(logging.DEBUG, message, extra)

def auth_error(message: str, extra: Dict[str, Any] = None):
    """认证错误日志"""
    multi_logger.log_authentication(logging.ERROR, message, extra)

def auth_warning(message: str, extra: Dict[str, Any] = None):
    """认证警告日志"""
    multi_logger.log_authentication(logging.WARNING, message, extra)

# 计费日志函数
def acct_info(message: str, extra: Dict[str, Any] = None):
    """计费信息日志"""
    multi_logger.log_accounting(logging.INFO, message, extra)

def acct_debug(message: str, extra: Dict[str, Any] = None):
    """计费调试日志"""
    multi_logger.log_accounting(logging.DEBUG, message, extra)

def acct_error(message: str, extra: Dict[str, Any] = None):
    """计费错误日志"""
    multi_logger.log_accounting(logging.ERROR, message, extra)

def acct_warning(message: str, extra: Dict[str, Any] = None):
    """计费警告日志"""
    multi_logger.log_accounting(logging.WARNING, message, extra)

# COA日志函数
def coa_info(message: str, extra: Dict[str, Any] = None):
    """COA信息日志"""
    multi_logger.log_coa(logging.INFO, message, extra)

def coa_debug(message: str, extra: Dict[str, Any] = None):
    """COA调试日志"""
    multi_logger.log_coa(logging.DEBUG, message, extra)

def coa_error(message: str, extra: Dict[str, Any] = None):
    """COA错误日志"""
    multi_logger.log_coa(logging.ERROR, message, extra)

def coa_warning(message: str, extra: Dict[str, Any] = None):
    """COA警告日志"""
    multi_logger.log_coa(logging.WARNING, message, extra)

# 系统日志函数
def system_error(message: str, exc_info=None):
    """系统错误日志"""
    multi_logger.log_system_error(message, exc_info)

def system_info(message: str):
    """系统信息日志"""
    multi_logger.log_system_info(message)

# 包属性记录函数
def log_packet_attributes(pkt, title: str = "Packet Attributes"):
    """记录RADIUS包属性"""
    # title 参数保留用于兼容性
    _ = title
    auth_info("**************************************** [Attributes] ****************************************")

    # 按照特定顺序显示所有属性
    for attr in pkt.keys():
        try:
            value = pkt[attr]
            auth_info(f"{attr}: {value}")
        except Exception as e:
            auth_info(f"{attr}: <error reading value: {e}>")

# 认证步骤记录函数
def log_auth_step(step: str, result: str, details: Dict[str, Any] = None):
    """记录认证步骤"""
    message = f"{step}: {result}"
    if details:
        message += f" {details}"
    auth_info(message)

# 数据库操作记录函数
def log_database_operation(operation: str, table: str, duration_ms: float = None):
    """记录数据库操作"""
    message = f"DB {operation} on {table}"
    if duration_ms:
        message += f" ({duration_ms:.2f}ms)"
    auth_debug(message)
