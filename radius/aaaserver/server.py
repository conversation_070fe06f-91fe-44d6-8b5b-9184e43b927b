#!/usr/bin/python
from __future__ import print_function

import sys
import os
# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import threading
from datetime import datetime, timedelta
import pytz
import configparser

from pyrad import dictionary, packet, server

import dbutil
from mysql.connector import Error
import ipaddress

import logging
import logging.config

logging.config.fileConfig('conf/logging.conf')

# 读取AAA配置
auth_config = configparser.ConfigParser()
auth_config.read('conf/aaa_config.ini')

# 全局变量存储VPDN域名列表
vpdn_domains = set()


def is_auth_check_enabled(check_type):
    """
    检查指定的认证检查是否启用

    Args:
        check_type (str): 检查类型 ('username_check', 'password_check', 'user_status_check',
                         'online_limit_check', 'domain_check')

    Returns:
        bool: True表示启用，False表示禁用
    """
    try:
        return auth_config.getboolean('authentication', f'{check_type}_enabled')
    except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
        # 如果配置不存在或无效，默认启用
        logging.warning(f"Auth config for {check_type}_enabled not found or invalid, defaulting to enabled")
        return True


def get_auth_config_value(section, key, default_value, value_type='str'):
    """
    获取认证配置值

    Args:
        section (str): 配置段名
        key (str): 配置键名
        default_value: 默认值
        value_type (str): 值类型 ('str', 'int', 'float', 'bool')

    Returns:
        配置值或默认值
    """
    try:
        if value_type == 'int':
            return auth_config.getint(section, key)
        elif value_type == 'float':
            return auth_config.getfloat(section, key)
        elif value_type == 'bool':
            return auth_config.getboolean(section, key)
        else:
            return auth_config.get(section, key)
    except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
        logging.warning(f"Config {section}.{key} not found or invalid, using default: {default_value}")
        return default_value


def safe_get_packet_attr(pkt, attr_name, default_value=None, index=0):
    """
    安全地从RADIUS包中获取属性值

    Args:
        pkt: RADIUS包对象
        attr_name (str): 属性名称
        default_value: 默认值
        index (int): 属性值列表中的索引，默认为0

    Returns:
        属性值或默认值
    """
    try:
        if attr_name in pkt:
            attr_list = pkt[attr_name]
            if isinstance(attr_list, list) and len(attr_list) > index:
                value = attr_list[index]
            elif not isinstance(attr_list, list):
                value = attr_list
            else:
                return default_value

            # 如果是字符串且两端有双引号，则去除双引号
            if isinstance(value, str) and len(value) >= 2 and value.startswith('"') and value.endswith('"'):
                value = value[1:-1]

            return value
        return default_value
    except (KeyError, IndexError, AttributeError, TypeError) as e:
        logging.warning(f"Failed to get packet attribute '{attr_name}': {e}, using default: {default_value}")
        return default_value


def safe_get_packet_attr_list(pkt, attr_name, default_value=None):
    """
    安全地从RADIUS包中获取属性值列表

    Args:
        pkt: RADIUS包对象
        attr_name (str): 属性名称
        default_value: 默认值（如果为None，则返回空列表）

    Returns:
        属性值列表或默认值
    """
    try:
        if attr_name in pkt:
            return pkt[attr_name]
        return default_value if default_value is not None else []
    except (KeyError, AttributeError, TypeError) as e:
        logging.warning(f"Failed to get packet attribute list '{attr_name}': {e}, using default: {default_value}")
        return default_value if default_value is not None else []


def parse_user_name(user_name_value):
    """
    解析用户名，分离用户名和域名

    Args:
        user_name_value (str): 完整的用户名字符串，可能包含域名

    Returns:
        tuple: (user_name, user_domain) 用户名和域名的元组
               如果没有域名，user_domain 为 None
               如果输入为空，两者都为 None
    """
    if not user_name_value:
        return None, None

    if '@' in user_name_value:
        user_name, user_domain = user_name_value.split('@', 1)
        return user_name.strip(), user_domain.strip()
    else:
        return user_name_value.strip(), None


def get_user_name_from_packet(pkt):
    """
    从RADIUS包中提取并解析用户名

    Args:
        pkt: RADIUS包对象

    Returns:
        tuple: (user_name, user_domain) 用户名和域名的元组
    """
    user_name_value = safe_get_packet_attr(pkt, 'User-Name')
    return parse_user_name(user_name_value)


def get_auth_type_from_packet(pkt):
    """
    从packet对象获取认证类型

    Args:
        pkt: AuthPacket对象，应该已经自动识别了认证类型

    Returns:
        int: 认证类型编码 (0=PAP, 1=CHAP)
    """
    if hasattr(pkt, 'auth_type'):
        if pkt.auth_type == 'pap':
            encrytype = 0
            logging.info(f"radius password encrypt type is pap (auto-detected)")
        elif pkt.auth_type == 'chap':
            encrytype = 1
            logging.info(f"radius password encrypt type is chap (auto-detected)")
        else:
            # 未知类型，回退到原来的检测方法
            encrytype = _legacy_detect_auth_type(pkt)
    else:
        # 没有auth_type属性，回退到原来的检测方法
        encrytype = _legacy_detect_auth_type(pkt)

    return encrytype


def _legacy_detect_auth_type(pkt):
    """
    传统的认证类型检测方法（备用）
    """
    user_password = pkt.get('User-Password', [None])[0]
    if user_password:
        encrytype = 0
        logging.info(f"radius password encrypt type is pap (legacy detection)")
    else:
        encrytype = 1
        logging.info(f"radius password encrypt type is chap (legacy detection)")
    return encrytype


def verify_user_password(pkt, user_info, encrytype):
    user_password = user_info.get('user_password', '')
    logging.info(f"user db password is : %s", user_password)
    if encrytype == 1:
        try:
            if not packet.AuthPacket.VerifyChapPasswd(pkt, user_password):
                logging.info(f"radius chap password is not equal db password")
                return 2
        except Exception as e:
            logging.error(f"Error verifying CHAP password: {e}")
            return 2
    else:
        try:
            user_passwd = safe_get_packet_attr(pkt, 'User-Password')
            if user_passwd is None:
                logging.warning("No User-Password attribute found in packet")
                return 2
            radius_passwd = packet.AuthPacket.PwDecrypt(pkt, user_passwd)
            logging.info(f"radius pap password is : %s", radius_passwd)
            if radius_passwd != user_password:
                logging.info(f"radius pap password is not equal db password")
                return 2
        except Exception as e:
            logging.error(f"Error verifying PAP password: {e}")
            return 2
    logging.info(f"password check passed!")
    return 0


def create_auth_reply(self, pkt, errorcode, user_info):
    reply = self.CreateReplyPacket(pkt)  # 使用 pkt 创建回复包
    match errorcode:
        case 0:
            logging.info(f"auth result : all passed!")
            logging.info("%s [authorise]  %s", 40 * "*", 40 * "*")

            # 获取授权配置
            session_timeout = get_auth_config_value('authorization', 'session_timeout', 172800, 'int')
            acct_interim_interval = get_auth_config_value('authorization', 'acct_interim_interval', 7200, 'int')
            framed_ip_netmask = get_auth_config_value('authorization', 'framed_ip_netmask', '***************')
            service_type = get_auth_config_value('authorization', 'service_type', 2, 'int')
            framed_protocol = get_auth_config_value('authorization', 'framed_protocol', 1, 'int')

            # 基础授权属性
            reply.update({
                "Service-Type": service_type,
                "Framed-Protocol": framed_protocol,
                "Framed-IP-Netmask": framed_ip_netmask,
                "Session-Timeout": session_timeout,
                "Acct-Interim-Interval": acct_interim_interval
            })
            reply.code = packet.AccessAccept

            # 处理Framed-IP-Address
            if user_info and user_info.get('user_bind_ip'):
                reply.update({
                    "Framed-IP-Address": user_info['user_bind_ip']
                })
                logging.info(f"author framedip : %s", user_info['user_bind_ip'])
            else:
                # 如果没有绑定IP，设置为Assigned（让设备分配）
                reply.update({
                    "Framed-IP-Address": '***************'
                })
                logging.info(f"author framedip : %s (Assigned)", '***************')

            bras_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address')
            if bras_ip and bras_ip in srv.hosts:
                try:
                    bras_vendor = srv.hosts[bras_ip].vendor
                    logging.info("bras vendor is %s", bras_vendor)
                    if bras_vendor == 'Huawei' and user_info:
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        if up_bandwidth and down_bandwidth:
                            reply.update({
                                "Huawei-Input-Average-Rate": int(up_bandwidth) * 1024,
                                "Huawei-Output-Average-Rate": int(down_bandwidth) * 1024
                            })
                            logging.info(f"author up bandwidth : %s", int(up_bandwidth) * 1024)
                            logging.info(f"author down bandwidth : %s", int(down_bandwidth) * 1024)

                        hw_domain_name = safe_get_packet_attr(pkt, 'HW-Domain-Name')
                        if hw_domain_name:
                            reply.update({
                                "HW-Domain-Name": hw_domain_name
                            })
                            logging.info(f"author HW-Domain-Name : %s", hw_domain_name)
                    elif bras_vendor == 'H3C' and user_info:
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        if up_bandwidth and down_bandwidth:
                            # H3C使用bps作为单位，直接使用kbps * 1000转换为bps
                            reply.update({
                                "Input-Average-Rate": int(up_bandwidth) * 1000,
                                "Output-Average-Rate": int(down_bandwidth) * 1000
                            })
                            logging.info(f"author H3C up bandwidth : %s bps", int(up_bandwidth) * 1000)
                            logging.info(f"author H3C down bandwidth : %s bps", int(down_bandwidth) * 1000)
                    elif bras_vendor == 'ZTE' and user_info:
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        if down_bandwidth and up_bandwidth:
                            reply.update({
                                "ZTE-Rate-Ctrl-Scr-Down": int(down_bandwidth),
                                "ZTE-Rate-Ctrl-Scr-Up": int(up_bandwidth)
                            })
                            logging.info(f"author up bandwidth : %s", int(down_bandwidth))
                            logging.info(f"author down bandwidth : %s", int(up_bandwidth))
                    elif bras_vendor == 'TPLink' and user_info:
                        down_bandwidth = user_info.get('user_down_bandwidth', 0)
                        up_bandwidth = user_info.get('user_up_bandwidth', 0)
                        if down_bandwidth and up_bandwidth:
                            reply.update({
                                "Xmit-limit": int(down_bandwidth),
                                "Recv-limit": int(up_bandwidth)
                            })
                            logging.info(f"author up bandwidth : %s", int(down_bandwidth))
                            logging.info(f"author down bandwidth : %s", int(up_bandwidth))
                except (KeyError, AttributeError) as e:
                    logging.warning(f"Error accessing BRAS vendor info: {e}")
            elif bras_ip:
                logging.warning(f"BRAS IP {bras_ip} not found in hosts configuration")
        case 1:
            reply.update({"Reply-Message": "UserName_Err"})
            logging.info(f"auth result : UserName_Err")
            reply.code = packet.AccessReject
        case 2:
            reply.update({"Reply-Message": "Password_Err"})
            logging.info(f"auth result : Password_Err")
            reply.code = packet.AccessReject
        case 3:
            reply.update({"Reply-Message": "LimitUser_Err"})
            logging.info(f"auth result : LimitUser_Err")
            reply.code = packet.AccessReject
        case 4:
            reply.update({"Reply-Message": "UserStatus_Err"})
            logging.info(f"auth result : UserStatus_Err")
            reply.code = packet.AccessReject
        case 5:
            reply.update({"Reply-Message": "Domain_Not_Exist"})
            logging.info(f"auth result : Domain_Not_Exist")
            reply.code = packet.AccessReject
        case 6:
            reply.update({"Reply-Message": "UserStatus_Paused"})
            logging.info(f"auth result : UserStatus_Paused")
            reply.code = packet.AccessReject
        case 7:
            reply.update({"Reply-Message": "UserStatus_Arrears"})
            logging.info(f"auth result : UserStatus_Arrears")
            reply.code = packet.AccessReject
        case _:
            reply.update({"Reply-Message": "Unknown_Error"})
            logging.info(f"auth result : Unknown_Error")
            reply.code = packet.AccessReject
    return reply


class RadiusServer(server.Server):

    def HandleAuthPacket(self, pkt):

        logging.info("Received an authentication request from %s:%s", pkt.source[0], pkt.source[1])
        logging.info("%s [Attributes] %s", 40 * "*", 40 * "*")
        for attr in pkt.keys():
            logging.info("%s: %s" % (attr, pkt[attr]))

        logging.info("%s [authenticate]  %s", 40 * "*", 40 * "*")
        now = datetime.now(pytz.timezone('Asia/Shanghai'))

        # 安全地获取认证记录所需的属性
        bras_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address')
        bras_port_raw = safe_get_packet_attr(pkt, 'NAS-Port')
        bras_port = None
        if bras_port_raw is not None:
            try:
                bras_port = int(bras_port_raw)
            except (ValueError, TypeError):
                logging.warning(f"Invalid NAS-Port value: {bras_port_raw}")
                bras_port = None

        record = {
            'bras_ip': bras_ip,
            'bras_port': bras_port,
            'bras_port_type': safe_get_packet_attr(pkt, 'NAS-Port-Type'),
            'line_info': safe_get_packet_attr(pkt, 'NAS-Port-Id'),
            'mac': safe_get_packet_attr(pkt, 'Calling-Station-Id'),
            'radius_server': self.hostname,
            'auth_date': now
        }

        user_name, user_domain = get_user_name_from_packet(pkt)
        if user_name:
            record['user_name'] = user_name
            record['dail_user_name'] = user_name

        if user_domain:
            record['user_domain'] = user_domain
        else:
            record['user_domain'] = ''

        # 域名存在性验证
        if user_domain and is_auth_check_enabled('domain_check'):
            if user_domain not in vpdn_domains:
                errorcode = 5  # 新增错误码：域名不存在
                record['user_business_type'] = 0
                logging.info(f"domain check failed! domain '{user_domain}' does not exist in vpdn table")
                reply = create_auth_reply(self, pkt, errorcode, None)
                self.SendReplyPacket(pkt.fd, reply)
                record['auth_result_code'] = errorcode

                def async_insert_record():
                    try:
                        dbutil.insert_authrecord(record)
                    except Error as e:
                        logging.info(f"Error inserting data in async_insert_record: {e}")

                logging.info("%s [recording]  %s", 40 * "*", 40 * "*")
                thread = threading.Thread(target=async_insert_record)
                thread.start()
                return
        elif user_domain and not is_auth_check_enabled('domain_check'):
            logging.info(f"domain check disabled, skipping domain validation for '{user_domain}'")

        # 用户名验证
        if is_auth_check_enabled('username_check'):
            user_info = dbutil.get_user_info(user_name, user_domain)
            encrytype = get_auth_type_from_packet(pkt)

            if not user_info:
                errorcode = 1
                record['user_business_type'] = 0
                logging.info(f"can not find user")
            else:
                logging.info(f"user name check passed! ")
                record['user_business_type'] = user_info['user_business_type']

                # 检查用户状态，只有状态为1的用户才能认证
                if is_auth_check_enabled('user_status_check'):
                    user_status = user_info.get('user_status', 2)  # 默认为2（暂停状态）
                    if user_status == 1:
                        logging.info(f"user status check passed! user_status: {user_status}")
                        errorcode = 0
                    elif user_status == 2:
                        errorcode = 6  # 用户暂停
                        logging.info(f"user status check failed! user_status: {user_status} (user paused)")
                    elif user_status == 4:
                        errorcode = 7  # 用户欠费
                        logging.info(f"user status check failed! user_status: {user_status} (user arrears)")
                    else:
                        errorcode = 4  # 其他用户状态错误
                        logging.info(f"user status check failed! user_status: {user_status}, only status 1 is allowed")
                else:
                    logging.info(f"user status check disabled, skipping user status validation")
                    errorcode = 0

                # 密码验证
                if errorcode == 0 and is_auth_check_enabled('password_check'):
                    errorcode = verify_user_password(pkt, user_info, encrytype)
                elif errorcode == 0 and not is_auth_check_enabled('password_check'):
                    logging.info(f"password check disabled, skipping password validation")

                # 只有前面验证通过后才检查在线数量限制
                if errorcode == 0 and is_auth_check_enabled('online_limit_check'):
                    online_count = dbutil.get_online_count_by_user(user_name, user_domain, user_info['user_business_type'])
                    if online_count > user_info.get('user_allow_onlinenums', 0):
                        errorcode = 3
                elif errorcode == 0 and not is_auth_check_enabled('online_limit_check'):
                    logging.info(f"online limit check disabled, skipping online count validation")
        else:
            logging.info(f"username check disabled, skipping user validation")
            # 如果用户名检查被禁用，创建一个基本的用户信息用于授权
            user_info = {'user_bind_ip': None, 'user_business_type': 0}
            record['user_business_type'] = 0
            errorcode = 0
        reply = create_auth_reply(self, pkt, errorcode, user_info)
        self.SendReplyPacket(pkt.fd, reply)
        record['auth_result_code'] = errorcode

        # 如果认证成功，收集授权属性
        if errorcode == 0 and reply.code == packet.AccessAccept:
            try:
                # 从回复包中提取授权属性，直接使用字符串值
                record['service_type'] = reply.get('Service-Type', [None])[0] if 'Service-Type' in reply else None
                record['framed_protocol'] = reply.get('Framed-Protocol', [None])[0] if 'Framed-Protocol' in reply else None

                record['framed_ip_netmask'] = reply.get('Framed-IP-Netmask', [None])[0] if 'Framed-IP-Netmask' in reply else None
                record['session_timeout'] = reply.get('Session-Timeout', [None])[0] if 'Session-Timeout' in reply else None
                record['acct_interim_interval'] = reply.get('Acct-Interim-Interval', [None])[0] if 'Acct-Interim-Interval' in reply else None
                record['framed_ip_address'] = reply.get('Framed-IP-Address', [None])[0] if 'Framed-IP-Address' in reply else None

                # 从用户信息中提取带宽信息
                if user_info:
                    record['user_down_bandwidth'] = user_info.get('user_down_bandwidth')
                    record['user_up_bandwidth'] = user_info.get('user_up_bandwidth')
                    logging.info(f"User bandwidth - Down: {record.get('user_down_bandwidth')} kbps, Up: {record.get('user_up_bandwidth')} kbps")

                logging.info("Authorization attributes collected for database storage")
            except Exception as e:
                logging.error(f"Error collecting authorization attributes: {e}")

        def async_insert_record():
            try:
                dbutil.insert_authrecord(record)
            except Error as e:
                logging.info(f"Error inserting data in async_insert_record: {e}")

        logging.info("%s [recording]  %s", 40 * "*", 40 * "*")
        thread = threading.Thread(target=async_insert_record)
        thread.start()

    def HandleAcctPacket(self, pkt):
        logging.info("Received an accounting request from %s:%s", pkt.source[0], pkt.source[1])
        logging.info("%s [Attributes] %s", 40 * "*", 40 * "*")
        for attr in pkt.keys():
            logging.info("%s: %s" % (attr, pkt[attr]))

        reply = self.CreateReplyPacket(pkt)
        self.SendReplyPacket(pkt.fd, reply)

        logging.info("%s [acct handling] %s", 40 * "*", 40 * "*")
        now = datetime.now(pytz.timezone('Asia/Shanghai'))
        acct_type = safe_get_packet_attr(pkt, 'Acct-Status-Type')

        # 安全地获取计费相关的数值属性
        session_time = safe_get_packet_attr(pkt, 'Acct-Session-Time', 0)
        delay_time = safe_get_packet_attr(pkt, 'Acct-Delay-Time', 0)
        nas_port_raw = safe_get_packet_attr(pkt, 'NAS-Port', 0)

        # 安全转换数值
        try:
            session_time = int(session_time) if session_time is not None else 0
            delay_time = int(delay_time) if delay_time is not None else 0
            nas_port = int(nas_port_raw) if nas_port_raw is not None else 0
        except (ValueError, TypeError):
            logging.warning(f"Invalid numeric values in accounting packet")
            session_time = delay_time = nas_port = 0

        # 安全地获取流量统计
        try:
            output_octets = int(safe_get_packet_attr(pkt, 'Acct-Output-Octets', 0))
            input_octets = int(safe_get_packet_attr(pkt, 'Acct-Input-Octets', 0))
            input_gigawords = int(safe_get_packet_attr(pkt, 'Acct-Input-Gigawords', 0))
            output_gigawords = int(safe_get_packet_attr(pkt, 'Acct-Output-Gigawords', 0))
            output_packets = int(safe_get_packet_attr(pkt, 'Acct-Output-Packets', 0))
            input_packets = int(safe_get_packet_attr(pkt, 'Acct-Input-Packets', 0))
        except (ValueError, TypeError):
            logging.debug(f"Invalid traffic statistics in accounting packet")
            output_octets = input_octets = input_gigawords = output_gigawords = output_packets = input_packets = 0

        record = {
            'online_time': now - timedelta(seconds=session_time) - timedelta(seconds=delay_time),
            'offline_time': now,
            'duration': session_time + delay_time,
            'line_info': safe_get_packet_attr(pkt, 'NAS-Port-Id'),
            'mac': safe_get_packet_attr(pkt, 'Calling-Station-Id'),
            'bras_ip': safe_get_packet_attr(pkt, 'NAS-IP-Address'),
            'bras_port': nas_port,
            'bras_port_type': safe_get_packet_attr(pkt, 'NAS-Port-Type'),
            'session_id': safe_get_packet_attr(pkt, 'Acct-Session-Id'),
            'client_type': 0,
            'packet_process_time': now,
            'user_framedip': safe_get_packet_attr(pkt, 'Framed-IP-Address'),
            'user_ipv4_outoctets': output_octets + (4 * input_gigawords * 1024 * 1024 * 1024),
            'user_ipv4_inoctets': input_octets + (4 * output_gigawords * 1024 * 1024 * 1024),
            'user_ipv4_outpackets': output_packets,
            'user_ipv4_inpackets': input_packets,
            'packet_type': acct_type,
            'radius_server': self.hostname
        }

        user_name, user_domain = get_user_name_from_packet(pkt)
        record['user_name'] = user_name
        record['dail_user_name'] = user_name
        if user_domain:
            record['user_domain'] = user_domain
        else:
            record['user_domain'] = ''

        # 安全地处理IPv6相关属性
        framed_interface_id = safe_get_packet_attr(pkt, 'Framed-Interface-Id')
        ipv6_prefix = safe_get_packet_attr(pkt, 'Framed-IPv6-Prefix')
        user_delegated_ipv6prefix = safe_get_packet_attr(pkt, 'Delegated-IPv6-Prefix')

        if ipv6_prefix:
            try:
                ipv6_network = ipaddress.IPv6Network(ipv6_prefix, strict=False)
                if framed_interface_id:
                    try:
                        formatted_interface_id = framed_interface_id.hex().upper()
                        if len(formatted_interface_id) == 16:
                            formatted_interface_id = ':'.join([formatted_interface_id[i:i + 4] for i in range(0, 16, 4)])
                        network_address = str(ipv6_network.network_address)[:-1]
                        ipv6_address = f"{network_address}{formatted_interface_id}"
                        record['user_framedipv6'] = str(ipaddress.IPv6Address(ipv6_address))
                    except (AttributeError, ipaddress.AddressValueError) as e:
                        logging.warning(f"Error processing IPv6 interface ID: {e}")
            except (ipaddress.AddressValueError, TypeError) as e:
                logging.warning(f"Invalid Framed-IPv6-Prefix: {e}")

        if user_delegated_ipv6prefix:
            record['user_delegated_ipv6prefix'] = user_delegated_ipv6prefix

        user_info = dbutil.get_user_info(user_name, user_domain)
        if not user_info:
            record['user_business_type'] = 0
            record['down_reason'] = 9999
            logging.info("can not find user in db")
        else:
            record['user_business_type'] = user_info.get('user_business_type', 0)
            record['down_reason'] = safe_get_packet_attr(pkt, 'Acct-Terminate-Cause')
            record['user_area'] = user_info.get('user_area', '')

        # 安全地处理BRAS相关信息
        bras_ip = safe_get_packet_attr(pkt, 'NAS-IP-Address')
        bras = None
        acct_update_flag = 0

        if bras_ip and bras_ip in srv.hosts:
            try:
                bras = srv.hosts[bras_ip]
                logging.info("bras vendor is : %s", bras.vendor)
                record['bras_area'] = getattr(bras, 'area', 0)

                if bras.vendor == 'Huawei':
                    # 安全地获取华为设备的NAT和IPv6属性
                    nat_public_addr = safe_get_packet_attr(pkt, 'Huawei-NAT-Public-Address')
                    if nat_public_addr:
                        record['user_nat_framedip'] = nat_public_addr

                    record['user_nat_beginport'] = safe_get_packet_attr(pkt, 'Huawei-NAT-Start-Port', 0)
                    record['user_nat_endport'] = safe_get_packet_attr(pkt, 'Huawei-NAT-End-Port', 0)

                    # 安全地处理IPv6流量统计
                    try:
                        ipv6_input_octets = int(safe_get_packet_attr(pkt, 'Huawei-Acct-IPv6-Input-Octets', 0))
                        ipv6_input_gigawords = int(safe_get_packet_attr(pkt, 'Huawei-Acct-IPv6-Input-Gigawords', 0))
                        ipv6_output_octets = int(safe_get_packet_attr(pkt, 'Huawei-Acct-IPv6-Output-Octets', 0))
                        ipv6_output_gigawords = int(safe_get_packet_attr(pkt, 'Huawei-Acct-IPv6-Output-Gigawords', 0))

                        record['user_ipv6_inoctets'] = ipv6_input_octets + (4 * ipv6_input_gigawords * 1024 * 1024 * 1024)
                        record['user_ipv6_outoctets'] = ipv6_output_octets + (4 * ipv6_output_gigawords * 1024 * 1024 * 1024)
                        record['user_ipv6_inpackets'] = int(safe_get_packet_attr(pkt, 'Huawei-Acct-IPv6-Input-Packets', 0))
                        record['user_ipv6_outpackets'] = int(safe_get_packet_attr(pkt, 'Huawei-Acct-IPv6-Output-Packets', 0))
                        acct_update_flag = int(safe_get_packet_attr(pkt, 'Huawei-Acct-Update-Address', 0))
                    except (ValueError, TypeError):
                        logging.debug("Invalid Huawei IPv6 statistics values")
                        record['user_ipv6_inoctets'] = record['user_ipv6_outoctets'] = 0
                        record['user_ipv6_inpackets'] = record['user_ipv6_outpackets'] = 0

                elif bras.vendor == 'H3C':
                    # 安全地获取H3C设备的NAT和IPv6属性
                    h3c_nat_addr = safe_get_packet_attr(pkt, 'NAT-IP-Address')
                    if h3c_nat_addr:
                        record['user_nat_framedip'] = h3c_nat_addr

                    record['user_nat_beginport'] = safe_get_packet_attr(pkt, 'NAT-Start-Port', 0)
                    record['user_nat_endport'] = safe_get_packet_attr(pkt, 'NAT-End-Port', 0)

                    # 从Ip-Host-Addr属性中提取MAC地址
                    ip_host_addr = safe_get_packet_attr(pkt, 'Ip-Host-Addr')
                    if ip_host_addr and ' ' in ip_host_addr:
                        try:
                            # 格式为"A.B.C.D hh:hh:hh:hh:hh:hh"
                            parts = ip_host_addr.split(' ')
                            if len(parts) >= 2:
                                mac_addr = parts[1]
                                # 验证MAC地址格式
                                if len(mac_addr.replace(':', '')) == 12:
                                    record['mac'] = mac_addr
                                    logging.info(f"H3C MAC address extracted from Ip-Host-Addr: {mac_addr}")
                        except Exception as e:
                            logging.warning(f"Error extracting MAC from H3C Ip-Host-Addr: {e}")

                    # 安全地处理IPv6流量统计
                    try:
                        h3c_ipv6_input_octets = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Input-Octets', 0))
                        h3c_ipv6_input_gigawords = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Input-Gigawords', 0))
                        h3c_ipv6_output_octets = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Output-Octets', 0))
                        h3c_ipv6_output_gigawords = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Output-Gigawords', 0))

                        record['user_ipv6_inoctets'] = h3c_ipv6_input_octets + (4 * h3c_ipv6_input_gigawords * 1024 * 1024 * 1024)
                        record['user_ipv6_outoctets'] = h3c_ipv6_output_octets + (4 * h3c_ipv6_output_gigawords * 1024 * 1024 * 1024)
                        record['user_ipv6_inpackets'] = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Input-Packets', 0))
                        record['user_ipv6_outpackets'] = int(safe_get_packet_attr(pkt, 'Acct-IPv6-Output-Packets', 0))
                        acct_update_flag = int(safe_get_packet_attr(pkt, 'Acct-Update-Address', 0))
                    except (ValueError, TypeError):
                        logging.debug("Invalid H3C IPv6 statistics values")
                        record['user_ipv6_inoctets'] = record['user_ipv6_outoctets'] = 0
                        record['user_ipv6_inpackets'] = record['user_ipv6_outpackets'] = 0

                elif bras.vendor == 'ZTE':
                    # 安全地获取中兴设备的NAT和IPv6属性
                    zte_nat_addr = safe_get_packet_attr(pkt, 'ZTE-NAT-IP-Address')
                    if zte_nat_addr:
                        record['user_nat_framedip'] = zte_nat_addr

                    record['user_nat_beginport'] = safe_get_packet_attr(pkt, 'ZTE-NAT-start-Port', 0)
                    record['user_nat_endport'] = safe_get_packet_attr(pkt, 'ZTE-NAT-end-Port', 0)

                    # 安全地处理IPv6流量统计
                    try:
                        zte_v6_input_octets = int(safe_get_packet_attr(pkt, 'ZTE-v6-Input-Octets', 0))
                        zte_v6_input_giga = int(safe_get_packet_attr(pkt, 'ZTE-v6-Input-Giga-Octets', 0))
                        zte_v6_output_octets = int(safe_get_packet_attr(pkt, 'ZTE-v6-Output-Octets', 0))
                        zte_v6_output_giga = int(safe_get_packet_attr(pkt, 'ZTE-v6-Outut-Giga-Octets', 0))

                        record['user_ipv6_inoctets'] = zte_v6_input_octets * 4 * zte_v6_input_giga * 1024 * 1024 * 1024
                        record['user_ipv6_outoctets'] = zte_v6_output_octets * 4 * zte_v6_output_giga * 1024 * 1024 * 1024
                        record['user_ipv6_inpackets'] = int(safe_get_packet_attr(pkt, 'ZTE-v6-Input-Packets', 0))
                        record['user_ipv6_outpackets'] = int(safe_get_packet_attr(pkt, 'ZTE-v6-Output-Packets', 0))
                        acct_update_flag = int(safe_get_packet_attr(pkt, 'ZTE-Acct-Address-Change', 0))
                    except (ValueError, TypeError):
                        logging.debug("Invalid ZTE IPv6 statistics values")
                        record['user_ipv6_inoctets'] = record['user_ipv6_outoctets'] = 0
                        record['user_ipv6_inpackets'] = record['user_ipv6_outpackets'] = 0

            except (KeyError, AttributeError) as e:
                logging.warning(f"Error accessing BRAS info for {bras_ip}: {e}")
                record['bras_area'] = 0
        else:
            if bras_ip:
                logging.warning(f"BRAS IP {bras_ip} not found in hosts configuration")
            record['bras_area'] = 0

        if acct_update_flag:
            # 如果存在特定的属性，则更新onlinerecord的相应字段
            acct_update_record = {
                'user_nat_framedip': record.get('user_nat_framedip', None),
                'user_nat_beginport': record['user_nat_beginport'],
                'user_nat_endport': record['user_nat_endport'],
                # 'user_framedip': record['user_framedip'],
                'user_framedipv6': record.get('user_framedipv6', None),
                'user_delegated_ipv6prefix': record.get('user_delegated_ipv6prefix', None),
                'packet_type': acct_type,
                'packet_process_time': now,
                'radius_server': self.hostname
            }
        else:
            # 否则，只更新特定的流量统计字段
            acct_update_record = {
                'user_ipv4_outoctets': record['user_ipv4_outoctets'],
                'user_ipv4_inoctets': record.get('user_ipv4_inoctets', 0),
                'user_ipv4_outpackets': record.get('user_ipv4_outpackets', 0),
                'user_ipv4_inpackets': record.get('user_ipv4_inpackets', 0),
                'user_ipv6_outoctets': record.get('user_ipv6_outoctets', 0),
                'user_ipv6_inoctets': record.get('user_ipv6_inoctets', 0),
                'user_ipv6_outpackets': record.get('user_ipv6_outpackets', 0),
                'user_ipv6_inpackets': record.get('user_ipv6_inpackets', 0),
                'packet_type': acct_type,
                'packet_process_time': now,
                'radius_server': self.hostname
            }

            # 根据session_id查找对应的记录，执行更新操作
        session_id = safe_get_packet_attr(pkt, 'Acct-Session-Id')

        def async_insert_onlinerecord():
            try:
                # logging.info("Accounting record inserting into onlinerecord table\n\n")
                dbutil.insert_onlinerecord(record)
            except Error as e:
                logging.info(f"Error inserting data in async_insert_record: {e}")

        def async_update_onlinerecord_by_sessionid():
            try:
                if session_id:
                    dbutil.update_onlinerecord_by_sessionid(session_id, acct_update_record, record, acct_update_flag)
                # logging.info("Accounting record updating in onlinerecord table.")
            except Error as e:
                logging.info(f"Error inserting data in async_insert_record: {e}")

        def async_insert_detail():
            try:
                dbutil.insert_detail(record)
            except Error as e:
                logging.info(f"Error inserting data in async_insert_record: {e}")

        def async_delete_onlinerecord_by_sessionid(session_id):
            try:
                dbutil.delete_onlinerecord_by_sessionid(session_id)
            except Error as e:
                logging.info(f"Error deleting data from online_record: {e}")

        if acct_type == 'Start':
            thread = threading.Thread(target=async_insert_onlinerecord)
            thread.start()

        elif acct_type == 'Alive':
            thread = threading.Thread(target=async_update_onlinerecord_by_sessionid)
            thread.start()

        elif acct_type == 'Stop':
            # 修复空键名问题，应该是down_reason
            record['down_reason'] = safe_get_packet_attr(pkt, 'Acct-Terminate-Cause', 0)
            thread = threading.Thread(target=async_insert_detail)
            thread.start()
            if session_id:
                thread = threading.Thread(target=async_delete_onlinerecord_by_sessionid, args=(session_id,))
                thread.start()

    def HandleCoaPacket(self, pkt):
        logging.info("Received an coa request")
        logging.info("Attributes: ")
        for attr in pkt.keys():
            logging.info("%s: %s" % (attr, pkt[attr]))

        reply = self.CreateReplyPacket(pkt)
        self.SendReplyPacket(pkt.fd, reply)

    def HandleDisconnectPacket(self, pkt):
        logging.info("Received an disconnect request")
        logging.info("Attributes: ")
        for attr in pkt.keys():
            logging.info("%s: %s" % (attr, pkt[attr]))

        reply = self.CreateReplyPacket(pkt)
        reply.code = 45  # COA NAK
        self.SendReplyPacket(pkt.fd, reply)


if __name__ == '__main__':
    # 初始化VPDN域名列表
    try:
        vpdn_list = dbutil.get_vpdn_list()
        if vpdn_list:
            vpdn_domains.update(vpdn['vpdn_domain'] for vpdn in vpdn_list)
            logging.info(f"Loaded {len(vpdn_domains)} VPDN domains: {list(vpdn_domains)}")
        else:
            logging.warning("No VPDN domains found in database")
    except Exception as e:
        logging.error(f"Failed to load VPDN domains: {e}")

    # 初始化RADIUS服务器
    srv = RadiusServer(addresses=['0.0.0.0'],dict=dictionary.Dictionary("conf/dictionary"), coa_enabled=True)

    # 初始化BRAS列表
    try:
        bras_list = dbutil.get_bras_list()
        if bras_list:
            srv.hosts = {bras['bras_ip']: server.RemoteHost(bras['bras_ip'], bras['bras_secret'].encode(), bras['bras_ip'],
                                                            bras['bras_model'], bras['bras_vendor'], bras['bras_area'])
                         for bras in bras_list}
            logging.info(f"Loaded {len(bras_list)} BRAS devices")
        else:
            logging.warning("No BRAS devices found in database")
            srv.hosts = {}
    except Exception as e:
        logging.error(f"Failed to load BRAS devices: {e}")
        srv.hosts = {}

    # 启动服务器
    logging.info("Starting RADIUS server...")
    srv.Run()
