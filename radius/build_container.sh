#!/bin/bash

# 容器编译脚本
# 用于在Docker容器中编译RADIUS服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查环境
check_environment() {
    print_step "检查编译环境..."
    
    # 检查是否在radius目录
    if [[ ! -f "start_async_server.py" ]]; then
        print_error "请在radius目录下运行此脚本"
        exit 1
    fi
    
    # 检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装或不可用"
        exit 1
    fi
    
    # 检查Dockerfile是否存在
    if [[ ! -f "docker/Dockerfile_nuitka" ]]; then
        print_error "Dockerfile_nuitka 不存在"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 清理旧镜像
cleanup_old_images() {
    print_step "清理旧的Docker镜像..."
    
    # 删除旧的镜像（如果存在）
    if docker images | grep -q "radius-aaa-secure"; then
        print_info "删除旧的radius-aaa-secure镜像..."
        docker rmi radius-aaa-secure:latest || true
    fi
    
    # 清理构建缓存
    print_info "清理Docker构建缓存..."
    docker builder prune -f || true
    
    print_success "清理完成"
}

# 构建Docker镜像
build_docker_image() {
    print_step "开始Docker容器编译..."
    print_info "这可能需要几分钟时间，请耐心等待..."
    
    # 检查是否使用代理
    local proxy_args=""
    if [[ -n "${http_proxy:-}" ]]; then
        print_info "检测到代理设置，使用代理进行构建"
        proxy_args="--build-arg http_proxy=$http_proxy --build-arg https_proxy=$https_proxy --build-arg no_proxy=$no_proxy"
    else
        print_info "未检测到代理，使用清华源进行构建"
    fi
    
    # 构建镜像
    if docker build \
        $proxy_args \
        -f docker/Dockerfile_nuitka \
        -t radius-aaa-secure:latest \
        .; then
        print_success "Docker镜像构建成功！"
        return 0
    else
        print_error "Docker镜像构建失败"
        return 1
    fi
}

# 验证构建结果
verify_build() {
    print_step "验证构建结果..."
    
    # 检查镜像是否存在
    if ! docker images | grep -q "radius-aaa-secure"; then
        print_error "镜像 radius-aaa-secure:latest 不存在"
        return 1
    fi
    
    # 显示镜像信息
    print_info "镜像信息:"
    docker images | grep radius-aaa-secure
    
    # 测试容器启动（健康检查）
    print_info "测试容器健康检查功能..."
    if docker run --rm radius-aaa-secure:latest --health-check; then
        print_success "健康检查功能正常"
    else
        print_warning "健康检查失败，但这在没有正确配置文件时是正常的"
    fi
    
    print_success "构建验证完成"
}

# 创建部署包（从容器中提取）
create_deployment_package() {
    print_step "从容器中提取部署包..."

    local DEPLOY_DIR="radius_deploy_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$DEPLOY_DIR"

    # 创建临时容器并复制文件
    local container_id=$(docker create radius-aaa-secure:latest)

    # 复制可执行文件
    docker cp "$container_id:/app/radius_server" "$DEPLOY_DIR/"

    # 复制脚本（如果存在）
    docker cp "$container_id:/app/start.sh" "$DEPLOY_DIR/" 2>/dev/null || print_warning "start.sh 不存在，将创建默认版本"
    docker cp "$container_id:/app/health_check.sh" "$DEPLOY_DIR/" 2>/dev/null || print_warning "health_check.sh 不存在，将创建默认版本"

    # 复制配置文件
    docker cp "$container_id:/app/conf" "$DEPLOY_DIR/"

    # 创建logs目录
    mkdir -p "$DEPLOY_DIR/logs"

    # 如果脚本不存在，创建默认版本
    if [[ ! -f "$DEPLOY_DIR/start.sh" ]]; then
        cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash
# RADIUS服务器启动脚本

# 检查配置文件
if [[ ! -f "conf/aaa_config.ini" ]]; then
    echo "错误: 配置文件 conf/aaa_config.ini 不存在"
    exit 1
fi

# 确保logs目录存在
mkdir -p logs

# 启动服务器
echo "启动RADIUS服务器..."
./radius_server
EOF
        chmod +x "$DEPLOY_DIR/start.sh"
    fi

    if [[ ! -f "$DEPLOY_DIR/health_check.sh" ]]; then
        cat > "$DEPLOY_DIR/health_check.sh" << 'EOF'
#!/bin/bash
# RADIUS服务器健康检查脚本

./radius_server --health-check
EOF
        chmod +x "$DEPLOY_DIR/health_check.sh"
    fi

    # 清理临时容器
    docker rm "$container_id"
    
    # 创建README
    cat > "$DEPLOY_DIR/README.md" << 'EOF'
# RADIUS服务器部署包（容器编译版本）

## 文件说明
- `radius_server`: 编译后的可执行文件
- `conf/`: 配置文件目录
- `logs/`: 日志文件目录
- `start.sh`: 启动脚本
- `health_check.sh`: 健康检查脚本

## 部署步骤
1. 将整个目录复制到目标服务器
2. 修改 `conf/` 目录下的配置文件
3. 运行 `./start.sh` 启动服务

## 健康检查
运行 `./health_check.sh` 检查服务状态

## 端口说明
- 1812/udp: RADIUS认证端口
- 1813/udp: RADIUS计费端口  
- 3799/udp: RADIUS CoA端口

## 注意事项
- 确保logs目录有写入权限
- 配置文件中的数据库连接信息需要正确设置
EOF
    
    print_success "部署包已创建: $DEPLOY_DIR"
    print_info "部署包大小: $(du -sh $DEPLOY_DIR | cut -f1)"
}

# 显示使用说明
show_usage() {
    print_info "🚀 RADIUS容器编译脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --no-cleanup    不清理旧镜像"
    echo "  --no-extract    不提取部署包"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  http_proxy      HTTP代理地址"
    echo "  https_proxy     HTTPS代理地址"
    echo "  no_proxy        不使用代理的地址列表"
    echo ""
}

# 主函数
main() {
    local cleanup=true
    local extract=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-cleanup)
                cleanup=false
                shift
                ;;
            --no-extract)
                extract=false
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_info "🚀 RADIUS容器编译脚本启动"
    
    check_environment
    
    if [[ "$cleanup" == "true" ]]; then
        cleanup_old_images
    fi
    
    if build_docker_image; then
        verify_build
        
        # 部署包提取功能已移除，直接使用Docker镜像
        
        print_success "🎉 容器编译完成！"
        print_info "Docker镜像: radius-aaa-secure:latest"
        print_info "可以使用 docker-compose 启动服务"
    else
        print_error "❌ 容器编译失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
