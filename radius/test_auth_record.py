#!/usr/bin/env python3
"""
测试认证记录入库功能，验证session_id和user_area字段是否正确设置
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from radius.aaaserver import async_dbutil

async def test_auth_record_insertion():
    """测试认证记录插入功能"""
    print("开始测试认证记录插入功能...")
    
    # 测试记录1：用户存在的情况
    test_record_1 = {
        'user_name': 'testuser1',
        'user_domain': 'test.com',
        'user_business_type': 1,
        'auth_date': '2024-01-15 10:30:00',
        'auth_result_code': 0,
        'bras_ip': '***********',
        'bras_port': 1234,
        'bras_port_type': 'Ethernet',
        'line_info': 'GE0/0/1',
        'mac': 'AA:BB:CC:DD:EE:FF',
        'radius_server': 'test-server',
        'dail_user_name': 'testuser1',
        'session_id': 'TEST-SESSION-001',
        'user_area': 'AREA-001'  # 用户区域
    }
    
    # 测试记录2：用户不存在的情况
    test_record_2 = {
        'user_name': 'nonexistuser',
        'user_domain': 'test.com',
        'user_business_type': 0,
        'auth_date': '2024-01-15 10:35:00',
        'auth_result_code': 1,  # 用户不存在
        'bras_ip': '***********',
        'bras_port': 5678,
        'bras_port_type': 'Ethernet',
        'line_info': 'GE0/0/2',
        'mac': 'FF:EE:DD:CC:BB:AA',
        'radius_server': 'test-server',
        'dail_user_name': 'nonexistuser',
        'session_id': 'TEST-SESSION-002',
        'user_area': 'AREA-002'  # BRAS区域
    }
    
    try:
        # 插入测试记录1
        print("插入测试记录1（用户存在）...")
        result1 = await async_dbutil.insert_authrecord(test_record_1)
        if result1:
            print("✅ 测试记录1插入成功")
        else:
            print("❌ 测试记录1插入失败")
        
        # 插入测试记录2
        print("插入测试记录2（用户不存在）...")
        result2 = await async_dbutil.insert_authrecord(test_record_2)
        if result2:
            print("✅ 测试记录2插入成功")
        else:
            print("❌ 测试记录2插入失败")
            
        print("测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def test_bras_area_query():
    """测试BRAS区域查询功能"""
    print("\n开始测试BRAS区域查询功能...")
    
    try:
        # 测试查询BRAS区域
        test_bras_ip = '***********'
        bras_area = await async_dbutil.get_bras_area_by_ip(test_bras_ip)
        
        if bras_area:
            print(f"✅ 查询到BRAS {test_bras_ip} 的区域: {bras_area}")
        else:
            print(f"❌ 未找到BRAS {test_bras_ip} 的区域信息")
            
    except Exception as e:
        print(f"❌ BRAS区域查询测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("=" * 60)
    print("认证记录入库功能测试")
    print("=" * 60)
    
    # 测试BRAS区域查询
    await test_bras_area_query()
    
    # 测试认证记录插入
    await test_auth_record_insertion()
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
