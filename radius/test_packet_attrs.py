#!/usr/bin/env python3
"""
测试RADIUS包属性提取功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from pyrad import dictionary, packet

def test_packet_attrs():
    """测试RADIUS包属性提取"""
    print("开始测试RADIUS包属性提取...")
    
    try:
        # 创建字典
        dict_obj = dictionary.Dictionary("conf/dictionary")
        
        # 创建一个模拟的认证包
        auth_pkt = packet.AuthPacket(dict=dict_obj)
        auth_pkt.code = packet.AccessRequest
        
        # 添加测试属性
        auth_pkt['User-Name'] = 'testuser'
        auth_pkt['NAS-IP-Address'] = '***********'
        auth_pkt['NAS-Port'] = 1234
        auth_pkt['NAS-Port-Type'] = 'Ethernet'
        auth_pkt['NAS-Port-Id'] = 'GE0/0/1'
        auth_pkt['Calling-Station-Id'] = 'AA:BB:CC:DD:EE:FF'
        auth_pkt['Acct-Session-Id'] = 'TEST-SESSION-12345'  # 关键测试属性
        
        print("创建的测试包属性:")
        for attr in auth_pkt.keys():
            print(f"  {attr}: {auth_pkt[attr]}")
        
        # 测试快速属性提取方法
        def _fast_get_packet_attrs(pkt, attr_names):
            """快速批量获取报文属性"""
            result = {}
            for attr_name in attr_names:
                if attr_name in pkt:
                    try:
                        value = pkt[attr_name]
                        if isinstance(value, list) and len(value) == 1:
                            value = value[0]
                        # 快速去除双引号
                        if isinstance(value, str) and len(value) >= 2 and value[0] == '"' and value[-1] == '"':
                            value = value[1:-1]
                        result[attr_name] = value
                    except (IndexError, AttributeError):
                        result[attr_name] = None
                else:
                    result[attr_name] = None
            return result
        
        # 测试属性提取
        auth_attrs = _fast_get_packet_attrs(auth_pkt, [
            'NAS-IP-Address', 'NAS-Port', 'NAS-Port-Type',
            'NAS-Port-Id', 'Calling-Station-Id', 'User-Name', 'Acct-Session-Id'
        ])
        
        print("\n提取的属性:")
        for attr_name, attr_value in auth_attrs.items():
            print(f"  {attr_name}: {attr_value}")
        
        # 验证session_id是否正确提取
        session_id = auth_attrs.get('Acct-Session-Id')
        print(f"\n提取的Session ID: {session_id}")
        
        if session_id == 'TEST-SESSION-12345':
            print("✅ Session ID 提取成功！")
        else:
            print("❌ Session ID 提取失败！")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_packet_attrs()
