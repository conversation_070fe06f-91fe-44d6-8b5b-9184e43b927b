#!/usr/bin/env python3
"""
测试session_id修复是否正确工作
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from radius.aaaserver import async_dbutil

async def test_session_id_insertion():
    """测试session_id字段是否正确插入"""
    print("开始测试session_id插入功能...")
    
    # 创建测试记录
    test_record = {
        'user_name': 'test_session_user',
        'user_domain': 'test.com',
        'user_business_type': 1,
        'auth_date': datetime.now(),
        'auth_result_code': 0,
        'bras_ip': '*************',
        'bras_port': 1234,
        'bras_port_type': 'Ethernet',
        'line_info': 'GE0/0/1',
        'mac': 'AA:BB:CC:DD:EE:FF',
        'radius_server': 'test-server',
        'dail_user_name': 'test_session_user',
        'session_id': 'TEST-SESSION-ID-12345',  # 明确设置session_id
        'user_area': 'TEST-AREA-001'
    }
    
    try:
        print(f"准备插入记录，session_id: {test_record['session_id']}")
        print(f"准备插入记录，user_area: {test_record['user_area']}")
        
        # 插入测试记录
        result = await async_dbutil.insert_authrecord(test_record)
        
        if result:
            print("✅ 测试记录插入成功")
            
            # 验证插入的记录
            print("正在验证插入的记录...")
            # 这里可以添加查询验证逻辑
            
        else:
            print("❌ 测试记录插入失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("=" * 60)
    print("Session ID 修复测试")
    print("=" * 60)
    
    await test_session_id_insertion()
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
