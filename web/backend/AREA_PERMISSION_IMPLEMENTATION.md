# 属地权限控制功能实现总结

## 概述

本次实现为VPDN管理系统添加了基于管理员属地的数据权限控制功能，实现了层级化的数据访问控制。

## 实现的功能

### 1. 数据库结构修改

#### 1.1 系统用户表添加属地字段
- **表名**: `s_system_users`
- **新增字段**: `user_area` (varchar(20))
- **外键关系**: 关联到 `s_system_area.code`
- **用途**: 存储管理员的属地代码

#### 1.2 相关表的属地字段
已存在的属地字段：
- `user` 表: `user_area` - 用户属地
- `bras` 表: `bras_area` - BRAS设备属地  
- `authrecord` 表: `user_area` - 认证记录用户属地
- `detail` 表: `user_area` - 清单记录用户属地
- `onlinerecord` 表: `user_area` - 在线记录用户属地
- `user_history` 表: `user_area` - 用户历史记录属地

### 2. 权限控制工具类

#### 2.1 AreaPermissionMixin 类
**文件位置**: `web/backend/dvadmin/utils/area_permission.py`

**主要方法**:
- `get_user_area_codes(user)`: 获取用户可管理的所有属地代码（包括下级属地）
- `filter_by_area_permission(queryset, user, area_field)`: 根据用户属地权限过滤查询集
- `filter_bras_by_area_permission(queryset, user)`: 专门用于BRAS设备的属地权限过滤
- `check_area_permission(user, target_area_code)`: 检查用户是否有权限管理指定属地的数据
- `get_area_choices_for_user(user)`: 获取用户可选择的属地选项

#### 2.2 权限验证函数
- `validate_area_permission(user, target_area_code, operation)`: 验证用户对指定属地的操作权限
- `get_area_hierarchy(area_code)`: 获取属地层级结构
- `get_area_name_by_code(area_code)`: 根据属地代码获取属地名称

### 3. 模型层修改

#### 3.1 Users 模型
**文件位置**: `web/backend/dvadmin/system/models.py`

**修改内容**:
```python
user_area = models.ForeignKey(
    to="Area",
    verbose_name="用户属地",
    to_field="code",
    on_delete=models.PROTECT,
    db_constraint=False,
    null=True,
    blank=True,
    help_text="用户属地",
)
```

### 4. 视图层修改

#### 4.1 VPDN相关ViewSet
**文件位置**: `web/backend/dvadmin/vpdn/views.py`

**修改的ViewSet**:
1. **VpdnUserViewSet** (用户管理)
   - 继承 `AreaPermissionMixin`
   - 在 `get_queryset()` 中应用属地权限过滤
   - 在 `perform_create()` 和 `perform_update()` 中验证属地权限
   - 添加 `area_choices()` API获取可选属地

2. **BrasViewSet** (BRAS设备管理)
   - 继承 `AreaPermissionMixin`
   - 在 `get_queryset()` 中应用BRAS属地权限过滤

3. **AuthRecordViewSet** (认证记录)
   - 继承 `AreaPermissionMixin`
   - 在 `get_queryset()` 中应用属地权限过滤

4. **DetailViewSet** (清单查询)
   - 继承 `AreaPermissionMixin`
   - 在 `get_queryset()` 中应用属地权限过滤

5. **OnlineRecordViewSet** (在线查询)
   - 继承 `AreaPermissionMixin`
   - 在 `get_queryset()` 中应用属地权限过滤

#### 4.2 系统用户ViewSet
**文件位置**: `web/backend/dvadmin/system/views/user.py`

**修改内容**:
- 在 `UserSerializer` 中添加 `user_area_name` 字段
- 在 `user_info()` 方法中返回用户属地信息
- 添加 `area_choices()` API获取可选属地

### 5. 权限控制逻辑

#### 5.1 层级权限控制
- **超级管理员**: 可以管理所有属地的数据
- **普通管理员**: 只能管理自己属地及下级属地的数据
- **无属地管理员**: 只能看到没有属地的数据

#### 5.2 数据过滤规则
- **用户管理**: 根据 `user.user_area` 过滤
- **BRAS管理**: 根据 `bras.bras_area` 过滤
- **认证记录**: 根据 `authrecord.user_area` 过滤
- **清单查询**: 根据 `detail.user_area` 过滤
- **在线查询**: 根据 `onlinerecord.user_area` 过滤
- **用户历史**: 根据 `user_history.user_area` 过滤

#### 5.3 操作权限验证
- **新增数据**: 验证目标属地是否在管理员权限范围内
- **编辑数据**: 验证属地变更是否在权限范围内
- **删除数据**: 通过查询过滤自动限制
- **查看数据**: 通过查询过滤自动限制

### 6. API接口

#### 6.1 属地选项API
- **用户管理**: `GET /api/vpdn/vpdnuser/area_choices/`
- **系统用户**: `GET /api/system/user/area_choices/`

**返回格式**:
```json
{
    "code": 2000,
    "msg": "获取成功",
    "data": [
        {
            "code": "5301",
            "name": "昆明市",
            "level": 2
        },
        {
            "code": "530102",
            "name": "五华区",
            "level": 3
        }
    ]
}
```

### 7. 数据库迁移

#### 7.1 迁移文件
- `dvadmin/system/migrations/0005_users_user_area.py`

#### 7.2 迁移内容
- 为 `s_system_users` 表添加 `user_area` 字段
- 建立与 `s_system_area` 表的外键关系

### 8. 使用示例

#### 8.1 在ViewSet中使用
```python
class MyViewSet(AreaPermissionMixin, ModelViewSet):
    def get_queryset(self):
        queryset = super().get_queryset()
        # 应用属地权限过滤
        queryset = self.filter_by_area_permission(queryset, self.request.user, 'user_area')
        return queryset
    
    def perform_create(self, serializer):
        user_area = serializer.validated_data.get('user_area')
        if user_area:
            has_permission, message = validate_area_permission(self.request.user, user_area, 'create')
            if not has_permission:
                raise ValidationError({'user_area': message})
        serializer.save()
```

#### 8.2 获取用户可选属地
```python
from dvadmin.utils.area_permission import AreaPermissionMixin

mixin = AreaPermissionMixin()
area_choices = mixin.get_area_choices_for_user(request.user)
```

### 9. 前端集成要点

#### 9.1 属地选择组件
- 调用 `/api/vpdn/vpdnuser/area_choices/` 获取可选属地
- 根据层级结构展示属地选项
- 在新增/编辑表单中限制属地选择范围

#### 9.2 查询条件限制
- 属地查询条件应该限制在用户权限范围内
- 不应该将属地作为必填的查询条件

### 10. 注意事项

#### 10.1 性能考虑
- 属地权限过滤会增加查询复杂度
- 建议在相关字段上建立索引
- 对于大数据量场景，考虑缓存属地层级关系

#### 10.2 数据一致性
- 确保所有相关数据都有正确的属地标识
- 在数据导入时需要设置正确的属地信息

#### 10.3 权限边界
- 超级管理员不受属地限制
- 普通管理员严格按照属地层级控制
- 无属地的历史数据需要特殊处理

## 总结

本次实现完成了完整的属地权限控制功能，涵盖了VPDN管理的所有核心模块。通过层级化的权限控制，确保管理员只能访问和操作其权限范围内的数据，提高了系统的安全性和数据隔离性。
