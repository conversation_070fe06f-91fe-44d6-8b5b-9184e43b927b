from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.viewsets import ModelViewSet
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Sum, Count
from datetime import datetime, timedelta
import hashlib
import pandas as pd
import io
from dvadmin.utils.json_response import DetailResponse, ErrorResponse
from dvadmin.utils.area_permission import AreaPermissionMixin, validate_area_permission

from .models import Domain, Bras, VpdnUser, VpdnUserHistory, AuthRecord, Detail, OnlineRecord
from .serializers import (
    DomainSerializer, BrasSerializer, VpdnUserSerializer, VpdnUserHistorySerializer,
    AuthRecordSerializer, DetailSerializer, OnlineRecordSerializer,
    BillQuerySerializer, TraceQuerySerializer, BatchOperationSerializer,
    ExportDataSerializer, ForceOfflineSerializer
)


class DomainViewSet(ModelViewSet):
    """域名管理视图集"""
    queryset = Domain.objects.all()
    serializer_class = DomainSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 通用搜索参数
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(vpdn_domain__icontains=search) |
                Q(vpdn_name__icontains=search) |
                Q(description__icontains=search)
            )

        # 具体字段搜索参数
        vpdn_domain = self.request.query_params.get('vpdn_domain')
        if vpdn_domain:
            queryset = queryset.filter(vpdn_domain__icontains=vpdn_domain)

        vpdn_name = self.request.query_params.get('vpdn_name')
        if vpdn_name:
            queryset = queryset.filter(vpdn_name__icontains=vpdn_name)      

        vpdn_areano = self.request.query_params.get('vpdn_areano')
        if vpdn_areano:
            queryset = queryset.filter(vpdn_areano__icontains=vpdn_areano)

        return queryset

    @action(detail=False, methods=['post'])
    def update_domain(self, request):
        """自定义更新接口 - 处理域名中的点号问题"""
        vpdn_domain = request.data.get('vpdn_domain')
        if not vpdn_domain:
            return Response({'error': '域名不能为空'}, status=400)

        try:
            domain = Domain.objects.get(vpdn_domain=vpdn_domain)
            serializer = DomainSerializer(domain, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=400)
        except Domain.DoesNotExist:
            return Response({'error': '域名不存在'}, status=404)

    @action(detail=False, methods=['post'])
    def delete_domain(self, request):
        """自定义删除接口 - 处理域名中的点号问题"""
        vpdn_domain = request.data.get('vpdn_domain')
        if not vpdn_domain:
            return Response({'error': '域名不能为空'}, status=400)

        try:
            domain = Domain.objects.get(vpdn_domain=vpdn_domain)
            domain.delete()
            return Response({'message': '删除成功'})
        except Domain.DoesNotExist:
            return Response({'error': '域名不存在'}, status=404)


class BrasViewSet(AreaPermissionMixin, ModelViewSet):
    """LNS设备管理视图集"""
    queryset = Bras.objects.all()
    serializer_class = BrasSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        # 应用属地权限过滤
        queryset = self.filter_bras_by_area_permission(queryset, self.request.user)

        # 通用搜索参数
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(bras_ip__icontains=search) |
                Q(bras_model__icontains=search) |
                Q(bras_vendor__icontains=search) |
                Q(bras_area__icontains=search)
            )

        # 具体字段搜索参数
        bras_ip = self.request.query_params.get('bras_ip')
        if bras_ip:
            queryset = queryset.filter(bras_ip__icontains=bras_ip)

        bras_vendor = self.request.query_params.get('bras_vendor')
        if bras_vendor:
            queryset = queryset.filter(bras_vendor__icontains=bras_vendor)

        bras_model = self.request.query_params.get('bras_model')
        if bras_model:
            queryset = queryset.filter(bras_model__icontains=bras_model)

        bras_area = self.request.query_params.get('bras_area')
        if bras_area:
            queryset = queryset.filter(bras_area__icontains=bras_area)

        return queryset

    @action(detail=False, methods=['post'])
    def update_bras(self, request):
        """更新BRAS设备"""
        bras_ip = request.data.get('bras_ip')
        if not bras_ip:
            return Response({'error': 'BRAS IP不能为空'}, status=400)

        try:
            bras = Bras.objects.get(bras_ip=bras_ip)
            serializer = BrasSerializer(bras, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({'message': '更新成功'})
            return Response({'error': '数据验证失败', 'details': serializer.errors}, status=400)
        except Bras.DoesNotExist:
            return Response({'error': 'BRAS设备不存在'}, status=404)

    @action(detail=False, methods=['post'])
    def delete_bras(self, request):
        """删除BRAS设备"""
        bras_ip = request.data.get('bras_ip')
        if not bras_ip:
            return Response({'error': 'BRAS IP不能为空'}, status=400)

        try:
            bras = Bras.objects.get(bras_ip=bras_ip)
            bras.delete()
            return Response({'message': '删除成功'})
        except Bras.DoesNotExist:
            return Response({'error': 'BRAS设备不存在'}, status=404)


class VpdnUserViewSet(AreaPermissionMixin, ModelViewSet):
    """VPDN用户管理视图集"""
    queryset = VpdnUser.objects.all()
    serializer_class = VpdnUserSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        """创建用户时自动设置操作员和时间，并校验IP绑定和属地权限"""
        # 获取要创建的用户数据
        user_domain = serializer.validated_data.get('user_domain')
        user_bind_ip = serializer.validated_data.get('user_bind_ip')
        user_area = serializer.validated_data.get('user_area')

        # 验证属地权限
        if user_area:
            has_permission, message = validate_area_permission(self.request.user, user_area, 'create')
            if not has_permission:
                from rest_framework.exceptions import ValidationError
                raise ValidationError({'user_area': message})

        # 校验同一域名下IP是否已被绑定
        if user_bind_ip and user_domain:
            existing_user = VpdnUser.objects.filter(
                user_domain=user_domain,
                user_bind_ip=user_bind_ip
            ).first()

            if existing_user:
                from rest_framework.exceptions import ValidationError
                raise ValidationError({
                    'user_bind_ip': f'IP地址 {user_bind_ip} 在域名 {user_domain} 下已被用户 {existing_user.user_name} 绑定，同一域名下不能重复绑定相同IP'
                })

        serializer.save(
            user_open_datetime=datetime.now(),
            user_open_operator=self.request.user.username if self.request.user else 'system'
        )

    def perform_update(self, serializer):
        """更新用户时自动设置修改操作员和时间，并校验IP绑定和属地权限"""
        # 获取当前用户实例和要更新的数据
        instance = serializer.instance
        user_domain = serializer.validated_data.get('user_domain', instance.user_domain)
        user_bind_ip = serializer.validated_data.get('user_bind_ip', instance.user_bind_ip)
        user_area = serializer.validated_data.get('user_area', instance.user_area)

        # 验证属地权限（如果属地发生变化）
        if user_area and user_area != instance.user_area:
            has_permission, message = validate_area_permission(self.request.user, user_area, 'update')
            if not has_permission:
                from rest_framework.exceptions import ValidationError
                raise ValidationError({'user_area': message})

        # 校验同一域名下IP是否已被其他用户绑定
        if user_bind_ip and user_domain:
            existing_user = VpdnUser.objects.filter(
                user_domain=user_domain,
                user_bind_ip=user_bind_ip
            ).exclude(id=instance.id).first()  # 排除当前用户

            if existing_user:
                from rest_framework.exceptions import ValidationError
                raise ValidationError({
                    'user_bind_ip': f'IP地址 {user_bind_ip} 在域名 {user_domain} 下已被用户 {existing_user.user_name} 绑定，同一域名下不能重复绑定相同IP'
                })

        serializer.save(
            user_modify_datetime=datetime.now(),
            user_modify_operator=self.request.user.username if self.request.user else 'system'
        )

    def get_queryset(self):
        queryset = super().get_queryset()

        # 应用属地权限过滤
        queryset = self.filter_by_area_permission(queryset, self.request.user, 'user_area')

        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_area__icontains=search) |
                Q(user_bind_ip__icontains=search)
            )

        # 具体字段搜索参数
        user_name = self.request.query_params.get('user_name')
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain__icontains=user_domain)

        user_status = self.request.query_params.get('user_status')
        if user_status:
            queryset = queryset.filter(user_status=user_status)

        user_area = self.request.query_params.get('user_area')
        if user_area:
            queryset = queryset.filter(user_area__icontains=user_area)

        user_bind_ip = self.request.query_params.get('user_bind_ip')
        if user_bind_ip:
            queryset = queryset.filter(user_bind_ip__icontains=user_bind_ip)

        user_business_type = self.request.query_params.get('user_business_type')
        if user_business_type:
            queryset = queryset.filter(user_business_type=user_business_type)

        return queryset

    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        """修改用户密码"""
        user = self.get_object()
        new_password = request.data.get('new_password')

        if not new_password:
            return Response({'error': '新密码不能为空'}, status=400)

        # 这里可以添加密码加密逻辑
        user.user_password = hashlib.md5(new_password.encode()).hexdigest()
        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'
        user.save()

        return Response({'message': '密码修改成功'})

    @action(detail=True, methods=['post'])
    def suspend(self, request, pk=None):
        """暂停用户"""
        user = self.get_object()
        user.user_status = 2  # 暂停状态
        user.user_pause_datetime = datetime.now()
        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'
        user.save()
        return Response({'message': '用户已暂停'})

    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """恢复用户"""
        user = self.get_object()
        user.user_status = 1  # 正常状态
        user.user_pause_datetime = None
        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'
        user.save()
        return Response({'message': '用户已恢复'})

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """注销用户"""
        user = self.get_object()
        user.user_status = 3  # 销户状态
        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'
        user.save()
        return Response({'message': '用户已注销'})

    @action(detail=True, methods=['post'])
    def bind_ip(self, request, pk=None):
        """绑定IP地址"""
        user = self.get_object()
        bind_ip = request.data.get('bind_ip')

        if bind_ip:
            # 校验同一域名下IP是否已被其他用户绑定
            existing_user = VpdnUser.objects.filter(
                user_domain=user.user_domain,
                user_bind_ip=bind_ip
            ).exclude(id=user.id).first()

            if existing_user:
                return Response({
                    'error': f'IP地址 {bind_ip} 在域名 {user.user_domain} 下已被用户 {existing_user.user_name} 绑定，同一域名下不能重复绑定相同IP'
                }, status=400)

            user.user_bind_ip = bind_ip
            user.user_modify_datetime = datetime.now()
            user.user_modify_operator = request.user.username if request.user else 'system'
            user.save()

        return DetailResponse(msg='IP绑定成功')

    @action(detail=True, methods=['post'])
    def change_status(self, request, pk=None):
        """切换用户状态"""
        user = self.get_object()
        new_status = request.data.get('status')

        # 移除销户状态(3)，只允许正常、暂停、欠费状态
        if new_status not in [1, 2, 4]:  # 1-正常, 2-暂停, 4-欠费
            return Response({'error': '无效的状态值'}, status=400)

        user.user_status = new_status
        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'

        if new_status == 2:  # 暂停状态
            user.user_pause_datetime = datetime.now()
        elif new_status == 1:  # 恢复正常
            user.user_pause_datetime = None

        user.save()

        status_map = {1: '正常', 2: '暂停', 4: '欠费'}
        return DetailResponse(msg=f'用户状态已切换为{status_map[new_status]}')

    @action(detail=True, methods=['post'])
    def change_area(self, request, pk=None):
        """移机 - 修改用户属地"""
        user = self.get_object()
        new_area = request.data.get('user_area')

        if not new_area:
            return Response({'error': '新属地不能为空'}, status=400)

        old_area = user.user_area
        user.user_area = new_area
        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'
        user.save()

        return DetailResponse(
            data={
                'old_area': old_area,
                'new_area': new_area
            },
            msg=f'用户属地已从 {old_area} 移机到 {new_area}'
        )

    @action(detail=True, methods=['post'])
    def change_bandwidth(self, request, pk=None):
        """修改用户带宽"""
        user = self.get_object()
        up_bandwidth = request.data.get('user_up_bandwidth')
        down_bandwidth = request.data.get('user_down_bandwidth')

        if up_bandwidth is not None:
            user.user_up_bandwidth = up_bandwidth
        if down_bandwidth is not None:
            user.user_down_bandwidth = down_bandwidth

        user.user_modify_datetime = datetime.now()
        user.user_modify_operator = request.user.username if request.user else 'system'
        user.save()

        return DetailResponse(
            data={
                'user_up_bandwidth': user.user_up_bandwidth,
                'user_down_bandwidth': user.user_down_bandwidth
            },
            msg='用户带宽修改成功'
        )

    @action(detail=False, methods=['post'])
    def batch_operation(self, request):
        """批量用户操作"""
        user_ids = request.data.get('user_ids', [])
        operation = request.data.get('operation')  # suspend, resume, cancel

        if not user_ids:
            return Response({'error': '请选择要操作的用户'}, status=400)

        if operation not in ['suspend', 'resume', 'cancel']:
            return Response({'error': '无效的操作类型'}, status=400)

        users = VpdnUser.objects.filter(id__in=user_ids)

        if operation == 'suspend':
            users.update(user_status=2)
            message = f'成功暂停 {users.count()} 个用户'
        elif operation == 'resume':
            users.update(user_status=1)
            message = f'成功恢复 {users.count()} 个用户'
        elif operation == 'cancel':
            users.update(user_status=3)
            message = f'成功注销 {users.count()} 个用户'

        return Response({'message': message})

    @action(detail=False, methods=['post'])
    def batch_add(self, request):
        """批量新增用户"""
        if 'file' not in request.FILES:
            return Response({'error': '请上传Excel文件'}, status=400)

        file = request.FILES['file']
        if not file.name.endswith(('.xlsx', '.xls')):
            return Response({'error': '请上传Excel格式文件'}, status=400)

        try:
            # 读取Excel文件
            df = pd.read_excel(file, sheet_name='用户信息')

            # 检查必要的列
            required_columns = ['用户名', '用户域名', '用户状态', '区域', '绑定IP', '上行带宽(Kbps)', '下行带宽(Kbps)']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return Response({
                    'error': f'Excel文件缺少必要的列: {", ".join(missing_columns)}'
                }, status=400)

            results = []
            success_count = 0
            fail_count = 0

            for index, row in df.iterrows():
                try:
                    # 验证必填字段
                    if pd.isna(row['用户名']) or pd.isna(row['用户域名']):
                        results.append({
                            'row': index + 2,  # Excel行号从2开始（第1行是表头）
                            'user_name': row.get('用户名', ''),
                            'status': 'error',
                            'message': '用户名和用户域名不能为空'
                        })
                        fail_count += 1
                        continue

                    # 检查用户是否已存在
                    user_name = str(row['用户名']).strip()
                    user_domain = str(row['用户域名']).strip()

                    # 校验域名是否存在
                    if not Domain.objects.filter(vpdn_domain=user_domain).exists():
                        results.append({
                            'row': index + 2,
                            'user_name': user_name,
                            'status': 'error',
                            'message': f'域名 {user_domain} 不存在，请先在域名管理中添加该域名'
                        })
                        fail_count += 1
                        continue

                    if VpdnUser.objects.filter(
                        user_name=user_name,
                        user_domain=user_domain,
                        user_business_type=4
                    ).exists():
                        results.append({
                            'row': index + 2,
                            'user_name': user_name,
                            'status': 'error',
                            'message': '用户已存在'
                        })
                        fail_count += 1
                        continue

                    # 校验IP绑定重复
                    user_bind_ip = str(row.get('绑定IP', '')).strip() if not pd.isna(row.get('绑定IP')) else None
                    if user_bind_ip:
                        existing_ip_user = VpdnUser.objects.filter(
                            user_domain=user_domain,
                            user_bind_ip=user_bind_ip
                        ).first()

                        if existing_ip_user:
                            results.append({
                                'row': index + 2,
                                'user_name': user_name,
                                'status': 'error',
                                'message': f'IP地址 {user_bind_ip} 在域名 {user_domain} 下已被用户 {existing_ip_user.user_name} 绑定，同一域名下不能重复绑定相同IP'
                            })
                            fail_count += 1
                            continue

                    # 创建用户数据
                    user_data = {
                        'user_name': user_name,
                        'user_domain': user_domain,
                        'user_business_type': 4,  # 默认业务类型为4
                        'user_status': int(row.get('用户状态', 1)),
                        'user_area': str(row.get('区域', '')).strip(),
                        'user_bind_ip': user_bind_ip,
                        'user_up_bandwidth': int(row.get('上行带宽(Kbps)', 0)) if not pd.isna(row.get('上行带宽(Kbps)')) else None,
                        'user_down_bandwidth': int(row.get('下行带宽(Kbps)', 0)) if not pd.isna(row.get('下行带宽(Kbps)')) else None,
                        'user_open_datetime': datetime.now(),
                        'user_open_operator': request.user.username if request.user else 'system',
                        'user_password': hashlib.md5(f"{user_name}123456".encode()).hexdigest(),  # 默认密码
                        'user_password_type': 'md5'
                    }

                    # 创建用户
                    user = VpdnUser.objects.create(**user_data)

                    results.append({
                        'row': index + 2,
                        'user_name': user_name,
                        'status': 'success',
                        'message': '创建成功'
                    })
                    success_count += 1

                except Exception as e:
                    results.append({
                        'row': index + 2,
                        'user_name': row.get('用户名', ''),
                        'status': 'error',
                        'message': f'创建失败: {str(e)}'
                    })
                    fail_count += 1

            return Response({
                'results': results,
                'summary': {
                    'total': len(df),
                    'success': success_count,
                    'fail': fail_count
                },
                'message': f'批量开通完成！成功：{success_count}个，失败：{fail_count}个'
            })

        except Exception as e:
            return Response({
                'error': f'文件处理失败: {str(e)}'
            }, status=400)

    @action(detail=False, methods=['get'])
    def user_statistics(self, request):
        """用户统计信息"""
        from django.db.models import Count

        stats = VpdnUser.objects.aggregate(
            total_users=Count('id'),
            active_users=Count('id', filter=Q(user_status=1)),
            suspended_users=Count('id', filter=Q(user_status=2)),
            cancelled_users=Count('id', filter=Q(user_status=3)),
            overdue_users=Count('id', filter=Q(user_status=4))
        )

        # 按域名统计
        domain_stats = VpdnUser.objects.values('user_domain').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        # 按区域统计
        area_stats = VpdnUser.objects.values('user_area').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        return Response({
            'user_stats': stats,
            'domain_stats': list(domain_stats),
            'area_stats': list(area_stats)
        })

    @action(detail=True, methods=['get'])
    def history(self, request, pk=None):
        """获取用户历史记录"""
        user = self.get_object()

        # 获取该用户的所有历史记录
        history_records = VpdnUserHistory.objects.filter(
            user_id=user.id
        ).order_by('-operation_time')

        # 分页处理
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        start = (page - 1) * page_size
        end = start + page_size

        total = history_records.count()
        records = history_records[start:end]

        serializer = VpdnUserHistorySerializer(records, many=True)

        return Response({
            'records': serializer.data,
            'total': total,
            'page': page,
            'page_size': page_size,
            'has_next': end < total,
            'has_previous': page > 1
        })

    @action(detail=False, methods=['get'])
    def area_choices(self, request):
        """获取用户可选择的属地选项"""
        area_choices = self.get_area_choices_for_user(request.user)
        return DetailResponse(data=list(area_choices), msg="获取成功")


class VpdnUserHistoryViewSet(ModelViewSet):
    """VPDN用户历史视图集"""
    queryset = VpdnUserHistory.objects.all()
    serializer_class = VpdnUserHistorySerializer
    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """重写list方法以返回正确的分页格式"""
        queryset = self.filter_queryset(self.get_queryset())

        # 分页处理
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 20))
        start = (page - 1) * page_size
        end = start + page_size

        total = queryset.count()
        records = queryset[start:end]

        serializer = self.get_serializer(records, many=True)

        return Response({
            'code': 2000,
            'msg': 'success',
            'data': {
                'results': serializer.data,
                'count': total,
                'page': page,
                'page_size': page_size
            },
            'total': total,
            'page': page,
            'limit': page_size
        })

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-operation_time')

        # 搜索条件
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(operation_user__icontains=search)
            )

        # 过滤条件
        user_name = self.request.query_params.get('user_name')
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain__icontains=user_domain)

        operation_type = self.request.query_params.get('operation_type')
        if operation_type:
            queryset = queryset.filter(operation_type=operation_type)

        operation_user = self.request.query_params.get('operation_user')
        if operation_user:
            queryset = queryset.filter(operation_user__icontains=operation_user)

        # 时间范围过滤
        start_time = self.request.query_params.get('start_time')
        end_time = self.request.query_params.get('end_time')

        # 如果没有设置时间段查询条件，默认查询最近两周的数据
        if not start_time and not end_time:
            two_weeks_ago = datetime.now() - timedelta(weeks=2)
            queryset = queryset.filter(operation_time__gte=two_weeks_ago)
        else:
            if start_time:
                queryset = queryset.filter(operation_time__gte=start_time)
            if end_time:
                queryset = queryset.filter(operation_time__lte=end_time)

        return queryset

    def create(self, request, *args, **kwargs):
        """禁止通过API创建历史记录"""
        return Response({'error': '不允许手动创建历史记录'}, status=400)

    def update(self, request, *args, **kwargs):
        """禁止通过API更新历史记录"""
        return Response({'error': '不允许修改历史记录'}, status=400)

    def destroy(self, request, *args, **kwargs):
        """禁止通过API删除历史记录"""
        return Response({'error': '不允许删除历史记录'}, status=400)


class AuthRecordViewSet(AreaPermissionMixin, ModelViewSet):
    """认证记录查询视图集"""
    queryset = AuthRecord.objects.all()
    serializer_class = AuthRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-auth_date')

        # 应用属地权限过滤
        queryset = self.filter_by_area_permission(queryset, self.request.user, 'user_area')

        # 如果没有设置时间段查询条件，默认查询最近两周的数据
        auth_start_time = self.request.query_params.get('auth_start_time')
        auth_end_time = self.request.query_params.get('auth_end_time')

        if not auth_start_time and not auth_end_time:
            two_weeks_ago = datetime.now() - timedelta(weeks=2)
            queryset = queryset.filter(auth_date__gte=two_weeks_ago)
        else:
            if auth_start_time:
                queryset = queryset.filter(auth_date__gte=auth_start_time)
            if auth_end_time:
                queryset = queryset.filter(auth_date__lte=auth_end_time)

        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(bras_ip__icontains=search)
            )

        # 单独的查询条件
        user_name = self.request.query_params.get('user_name')
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)

        bras_ip = self.request.query_params.get('bras_ip')
        if bras_ip:
            queryset = queryset.filter(bras_ip__icontains=bras_ip)

        user_ip = self.request.query_params.get('user_ip')
        if user_ip:
            queryset = queryset.filter(user_ip__icontains=user_ip)

        # 过滤条件
        auth_result_code = self.request.query_params.get('auth_result_code')
        if auth_result_code:
            queryset = queryset.filter(auth_result_code=auth_result_code)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        user_area = self.request.query_params.get('user_area')
        if user_area:
            queryset = queryset.filter(user_area=user_area)

        return queryset

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """认证统计"""
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = self.get_queryset()

        if start_date:
            queryset = queryset.filter(auth_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(auth_date__lte=end_date)

        stats = queryset.aggregate(
            total_count=Count('id'),
            success_count=Count('id', filter=Q(auth_result_code=0)),  # 假设0表示成功
            failed_count=Count('id', filter=~Q(auth_result_code=0))   # 非0表示失败
        )

        stats['success_rate'] = (
            stats['success_count'] / stats['total_count'] * 100
            if stats['total_count'] > 0 else 0
        )

        return Response(stats)


class DetailViewSet(AreaPermissionMixin, ModelViewSet):
    """清单查询视图集"""
    queryset = Detail.objects.all()
    serializer_class = DetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-online_time')

        # 应用属地权限过滤
        queryset = self.filter_by_area_permission(queryset, self.request.user, 'user_area')

        # 时间范围搜索：查询开始时间 <= 上线时间 AND 查询结束时间 >= 下线时间
        # 即：会话完全在查询时间范围内
        query_start_time = self.request.query_params.get('query_start_time')
        query_end_time = self.request.query_params.get('query_end_time')
        time_point = self.request.query_params.get('time_point')

        # 如果没有设置时间段查询条件，默认查询最近两周的数据
        if not query_start_time and not query_end_time and not time_point:
            two_weeks_ago = datetime.now() - timedelta(weeks=2)
            queryset = queryset.filter(online_time__gte=two_weeks_ago)
        else:
            if query_start_time and query_end_time:
                # 会话完全在查询时间范围内的条件：
                # 查询开始时间 <= 上线时间 AND 下线时间 <= 查询结束时间
                queryset = queryset.filter(
                    online_time__gte=query_start_time,   # 上线时间 >= 查询开始时间
                    offline_time__lte=query_end_time     # 下线时间 <= 查询结束时间
                )

            # 时间点搜索：时间点 >= 上线时间 AND 时间点 <= 下线时间
            # 即：查找在指定时间点正在在线的用户
            if time_point:
                queryset = queryset.filter(
                    online_time__lte=time_point,   # 上线时间 <= 时间点
                    offline_time__gte=time_point   # 下线时间 >= 时间点
                )

        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_framedip__icontains=search) |
                Q(bras_ip__icontains=search)
            )

        # 具体字段搜索参数
        user_name = self.request.query_params.get('user_name')
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        bras_ip = self.request.query_params.get('bras_ip')
        if bras_ip:
            queryset = queryset.filter(bras_ip__icontains=bras_ip)

        user_framedip = self.request.query_params.get('user_framedip')
        if user_framedip:
            queryset = queryset.filter(user_framedip__icontains=user_framedip)

        user_area = self.request.query_params.get('user_area')
        if user_area:
            queryset = queryset.filter(user_area__icontains=user_area)

        return queryset

    @action(detail=False, methods=['post'])
    def bill_query(self, request):
        """账单查询"""
        serializer = BillQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': '参数错误', 'details': serializer.errors}, status=400)

        data = serializer.validated_data
        year = data['year']
        month = data['month']
        username = data.get('username')
        domain = data.get('domain')

        # 计算月份的开始和结束时间
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)

        queryset = Detail.objects.filter(
            online_time__gte=start_date,
            online_time__lt=end_date
        )

        if username:
            queryset = queryset.filter(user_name=username)
        if domain:
            queryset = queryset.filter(user_domain=domain)

        # 按用户聚合统计
        bills = queryset.values('user_name', 'user_domain').annotate(
            total_duration=Sum('duration'),
            total_ipv4_input=Sum('user_ipv4_inoctets'),
            total_ipv4_output=Sum('user_ipv4_outoctets'),
            total_ipv6_input=Sum('user_ipv6_inoctets'),
            total_ipv6_output=Sum('user_ipv6_outoctets'),
            session_count=Count('id')
        )

        # 转换单位
        for bill in bills:
            bill['total_duration_hours'] = (
                round(bill['total_duration'] / 3600, 2)
                if bill['total_duration'] else 0
            )
            bill['total_ipv4_input_mb'] = (
                round((bill['total_ipv4_input'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv4_output_mb'] = (
                round((bill['total_ipv4_output'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv6_input_mb'] = (
                round((bill['total_ipv6_input'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv6_output_mb'] = (
                round((bill['total_ipv6_output'] or 0) / (1024 * 1024), 2)
            )
            bill['total_traffic_mb'] = (
                bill['total_ipv4_input_mb'] + bill['total_ipv4_output_mb'] +
                bill['total_ipv6_input_mb'] + bill['total_ipv6_output_mb']
            )
            bill['account_month'] = f"{year}-{month:02d}"

        return Response(list(bills))

    @action(detail=False, methods=['get'])
    def traffic_statistics(self, request):
        """流量统计"""
        # 按日期统计流量
        from django.db.models import Sum
        from django.db.models.functions import TruncDate

        daily_traffic = Detail.objects.annotate(
            date=TruncDate('online_time')
        ).values('date').annotate(
            total_input_octets=Sum('user_ipv4_inoctets'),
            total_output_octets=Sum('user_ipv4_outoctets'),
            total_sessions=Count('id')
        ).order_by('-date')[:30]

        # 按用户域名统计
        domain_traffic = Detail.objects.values('user_domain').annotate(
            total_input_octets=Sum('user_ipv4_inoctets'),
            total_output_octets=Sum('user_ipv4_outoctets'),
            total_sessions=Count('id')
        ).order_by('-total_input_octets')[:10]

        return Response({
            'daily_traffic': list(daily_traffic),
            'domain_traffic': list(domain_traffic)
        })

    @action(detail=False, methods=['post'])
    def export_data(self, request):
        """导出数据"""
        # 获取搜索参数
        user_name = request.data.get('user_name')
        user_domain = request.data.get('user_domain')
        bras_ip = request.data.get('bras_ip')
        user_framedip = request.data.get('user_framedip')
        user_area = request.data.get('user_area')
        query_start_time = request.data.get('query_start_time')
        query_end_time = request.data.get('query_end_time')
        time_point = request.data.get('time_point')

        queryset = self.get_queryset()

        # 应用搜索条件
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)
        if bras_ip:
            queryset = queryset.filter(bras_ip__icontains=bras_ip)
        if user_framedip:
            queryset = queryset.filter(user_framedip__icontains=user_framedip)
        if user_area:
            queryset = queryset.filter(user_area__icontains=user_area)

        # 时间范围搜索
        if query_start_time and query_end_time:
            queryset = queryset.filter(
                online_time__gte=query_start_time,
                offline_time__lte=query_end_time
            )

        # 时间点搜索
        if time_point:
            queryset = queryset.filter(
                online_time__lte=time_point,
                offline_time__gte=time_point
            )

        # 限制导出数量
        queryset = queryset[:10000]

        data = []
        for record in queryset:
            # 获取区域名称
            area_name = record.user_area
            if record.user_area:
                # 这里可以添加区域编码到名称的转换逻辑
                area_name = record.user_area

            data.append({
                '用户名': record.user_name or '',
                '用户域名': f"{record.user_domain} ({record.user_domain})" if record.user_domain else '',
                '上线时间': record.online_time.strftime('%Y-%m-%d %H:%M:%S') if record.online_time else '',
                '下线时间': record.offline_time.strftime('%Y-%m-%d %H:%M:%S') if record.offline_time else '',
                '会话时长(秒)': record.duration or 0,
                '上行流量(kbps)': record.user_ipv4_inoctets or 0,
                '下行流量(kbps)': record.user_ipv4_outoctets or 0,
                'NAS IP': record.bras_ip or '',
                '用户IP': record.user_framedip or '',
                '区域': area_name or '',
                '线路信息': record.line_info or '',
                'MAC地址': record.mac or '',
                'BRAS端口': record.bras_port or '',
                'BRAS端口类型': record.bras_port_type or '',
                '会话ID': record.session_id or '',
                '下线原因': record.down_reason or '',
                'RADIUS服务器': record.radius_server or ''
            })

        return Response({
            'data': data,
            'total': len(data),
            'message': f'导出 {len(data)} 条记录'
        })


class OnlineRecordViewSet(AreaPermissionMixin, ModelViewSet):
    """在线查询视图集"""
    queryset = OnlineRecord.objects.all()
    serializer_class = OnlineRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-online_time')

        # 应用属地权限过滤
        queryset = self.filter_by_area_permission(queryset, self.request.user, 'user_area')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_framedip__icontains=search) |
                Q(user_framedipv6__icontains=search) |
                Q(mac__icontains=search)
            )

        # 具体字段搜索参数
        user_name = self.request.query_params.get('user_name')
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        bras_ip = self.request.query_params.get('bras_ip')
        if bras_ip:
            queryset = queryset.filter(bras_ip__icontains=bras_ip)

        user_framedip = self.request.query_params.get('user_framedip')
        if user_framedip:
            queryset = queryset.filter(user_framedip__icontains=user_framedip)

        mac = self.request.query_params.get('mac')
        if mac:
            queryset = queryset.filter(mac__icontains=mac)

        user_business_type = self.request.query_params.get('user_business_type')
        if user_business_type:
            queryset = queryset.filter(user_business_type=user_business_type)

        user_area = self.request.query_params.get('user_area')
        if user_area:
            queryset = queryset.filter(user_area=user_area)

        # 上线时间范围搜索
        online_start_time = self.request.query_params.get('online_start_time')
        online_end_time = self.request.query_params.get('online_end_time')

        if online_start_time and online_end_time:
            queryset = queryset.filter(
                online_time__gte=online_start_time,
                online_time__lte=online_end_time
            )

        return queryset

    @action(detail=False, methods=['post'])
    def trace_query(self, request):
        """溯源查询"""
        serializer = TraceQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': '参数错误', 'details': serializer.errors}, status=400)

        data = serializer.validated_data

        # 构建查询条件
        online_q = Q()
        detail_q = Q()

        # 基础查询条件
        if data.get('username'):
            online_q &= Q(user_name=data['username'])
            detail_q &= Q(user_name=data['username'])

        if data.get('ip_address'):
            online_q &= Q(user_framedip=data['ip_address'])
            detail_q &= Q(user_framedip=data['ip_address'])

        if data.get('ipv6_address'):
            online_q &= Q(user_framedipv6=data['ipv6_address'])
            detail_q &= Q(user_framedipv6=data['ipv6_address'])

        if data.get('mac_address'):
            online_q &= Q(mac=data['mac_address'])
            detail_q &= Q(mac=data['mac_address'])

        # 过滤条件
        if data.get('domain_filter'):
            online_q &= Q(user_domain=data['domain_filter'])
            detail_q &= Q(user_domain=data['domain_filter'])

        if data.get('lns_filter'):
            online_q &= Q(bras_ip=data['lns_filter'])
            detail_q &= Q(bras_ip=data['lns_filter'])

        # 时间条件
        if data.get('trace_time'):
            # 时间点溯源
            trace_time = data['trace_time']
            online_q &= Q(online_time__lte=trace_time)
            detail_q &= Q(online_time__lte=trace_time, offline_time__gte=trace_time)
        else:
            # 时间范围查询
            if data.get('start_time'):
                online_q &= Q(online_time__gte=data['start_time'])
                detail_q &= Q(online_time__gte=data['start_time'])

            if data.get('end_time'):
                online_q &= Q(online_time__lte=data['end_time'])
                detail_q &= Q(online_time__lte=data['end_time'])

        # 查询在线记录
        online_records = OnlineRecord.objects.filter(online_q)
        online_serializer = OnlineRecordSerializer(online_records, many=True)

        # 查询历史记录
        detail_records = Detail.objects.filter(detail_q)
        detail_serializer = DetailSerializer(detail_records, many=True)

        return Response({
            'online_records': online_serializer.data,
            'detail_records': detail_serializer.data,
            'total_online': online_records.count(),
            'total_detail': detail_records.count()
        })

    @action(detail=False, methods=['get'])
    def real_time_monitor(self, request):
        """实时监控"""
        # 在线用户统计
        online_stats = OnlineRecord.objects.aggregate(
            total_online=Count('session_id'),
            total_ipv4_users=Count('session_id', filter=Q(user_framedip__isnull=False)),
            total_ipv6_users=Count('session_id', filter=Q(user_framedipv6__isnull=False))
        )

        # 按BRAS设备统计在线用户
        bras_stats = OnlineRecord.objects.values('bras_ip').annotate(
            online_count=Count('session_id')
        ).order_by('-online_count')

        # 按域名统计在线用户
        domain_stats = OnlineRecord.objects.values('user_domain').annotate(
            online_count=Count('session_id')
        ).order_by('-online_count')[:10]

        # 最近上线的用户
        recent_online = OnlineRecord.objects.order_by('-online_time')[:10]
        recent_data = []
        for record in recent_online:
            recent_data.append({
                'user_name': record.user_name,
                'user_domain': record.user_domain,
                'online_time': record.online_time.strftime('%Y-%m-%d %H:%M:%S') if record.online_time else '',
                'nas_ip': record.bras_ip,
                'framed_ip': record.user_framedip
            })

        return Response({
            'online_stats': online_stats,
            'bras_stats': list(bras_stats),
            'domain_stats': list(domain_stats),
            'recent_online': recent_data
        })

    @action(detail=False, methods=['post'])
    def force_offline(self, request):
        """强制下线用户 - 发送DM请求给radius server，不删除在线记录"""
        user_name = request.data.get('user_name')
        user_domain = request.data.get('user_domain')
        session_id = request.data.get('session_id')  # 新增：指定要踢下线的session_id
        reason = request.data.get('reason', '管理员强制下线')

        if not user_name or not user_domain:
            return Response({'error': '用户名和域名不能为空'}, status=400)

        # 如果指定了session_id，只查找该session
        if session_id:
            online_record = OnlineRecord.objects.filter(
                session_id=session_id,
                user_name=user_name,
                user_domain=user_domain
            ).first()

            if not online_record:
                return Response({'error': '指定的会话不存在或已下线'}, status=404)

            online_records = [online_record]
        else:
            # 兼容旧版本：如果没有指定session_id，踢所有会话
            online_records = OnlineRecord.objects.filter(
                user_name=user_name,
                user_domain=user_domain
            )

            if not online_records.exists():
                return Response({'error': '用户不在线'}, status=404)

            online_records = list(online_records)

        count = len(online_records)

        # 发送DM请求给radius server
        try:
            # 调用CoA客户端发送断开连接请求
            result = self._send_disconnect_requests(online_records, user_name, user_domain)

            if result['status'] == 'success':
                return Response({
                    'message': f'强制下线成功，会话 {online_records[0].session_id} 已断开连接'
                })
            elif result['status'] == 'timeout':
                return Response({
                    'error': f'强制下线失败：请求超时，RADIUS服务器未响应。会话 {online_records[0].session_id}'
                }, status=500)
            else:
                return Response({
                    'error': f'强制下线失败：{result.get("error", "未知错误")}'
                }, status=500)

        except Exception as e:
            return Response({
                'error': f'发送强制下线请求失败: {str(e)}'
            }, status=500)

    def _send_disconnect_requests(self, online_records, user_name, user_domain):
        """发送断开连接请求到RADIUS服务器"""
        import socket
        import time

        # RADIUS服务器配置
        radius_server = '**********'  # radius-aaa容器的IP地址
        radius_port = 3799  # CoA端口
        radius_secret = b'testing123'  # RADIUS共享密钥
        timeout = 3  # 单次请求超时时间（秒）
        max_retries = 2  # 最大重试次数（总共尝试3次）

        # 只处理第一个记录（因为现在按session_id踢下线）
        if not online_records:
            return {'status': 'error', 'error': '没有找到要踢下线的会话'}

        record = online_records[0]

        for attempt in range(max_retries):
            try:
                # 创建UDP socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(timeout)

                # 构建RADIUS Disconnect-Request包
                packet = self._build_disconnect_packet(record.session_id, radius_secret)

                # 发送请求
                sock.sendto(packet, (radius_server, radius_port))

                # 接收响应
                try:
                    response, addr = sock.recvfrom(4096)
                    # 解析响应
                    if len(response) >= 4:
                        code = response[0]
                        # Disconnect-ACK = 41, Disconnect-NAK = 42
                        if code == 41:  # Disconnect-ACK
                            sock.close()
                            return {'status': 'success'}
                        elif code == 42:  # Disconnect-NAK
                            sock.close()
                            return {'status': 'error', 'error': 'RADIUS服务器拒绝了断开连接请求'}
                        else:
                            sock.close()
                            return {'status': 'error', 'error': f'收到未知的响应代码: {code}'}
                    else:
                        sock.close()
                        return {'status': 'error', 'error': '收到无效的响应包'}

                except socket.timeout:
                    sock.close()
                    if attempt == max_retries - 1:  # 最后一次重试
                        return {
                            'status': 'timeout',
                            'error': f'RADIUS服务器响应超时，已重试 {max_retries + 1} 次（每次超时 {timeout} 秒）。请检查RADIUS服务器状态或网络连接'
                        }
                    # 继续重试
                    time.sleep(0.5)  # 重试前等待0.5秒
                    continue

            except Exception as e:
                if 'sock' in locals():
                    sock.close()
                if attempt == max_retries - 1:  # 最后一次重试
                    return {'status': 'error', 'error': f'发送请求失败: {str(e)}'}
                # 继续重试
                time.sleep(1)
                continue

        return {'status': 'error', 'error': '所有重试都失败了'}

    def _build_disconnect_packet(self, session_id, secret):
        """构建RADIUS Disconnect-Request包"""
        import hashlib
        import random
        import struct

        # RADIUS包头
        code = 40  # Disconnect-Request
        identifier = random.randint(0, 255)
        length = 20  # 基础长度，后面会更新
        authenticator = b'\x00' * 16  # 临时认证字段

        # 属性
        attributes = b''

        # Acct-Session-Id (44)
        session_id_bytes = session_id.encode('utf-8')
        attr_length = 2 + len(session_id_bytes)
        attributes += struct.pack('!BB', 44, attr_length) + session_id_bytes

        # 更新总长度
        length = 20 + len(attributes)

        # 构建包（不包含最终的认证字段）
        packet_without_auth = struct.pack('!BBH', code, identifier, length) + authenticator + attributes

        # 计算Request Authenticator (MD5)
        md5 = hashlib.md5()
        md5.update(packet_without_auth)
        md5.update(secret)
        request_authenticator = md5.digest()

        # 构建最终包
        final_packet = struct.pack('!BBH', code, identifier, length) + request_authenticator + attributes

        return final_packet

    def _build_disconnect_packet(self, session_id, secret):
        """构建RADIUS Disconnect-Request包"""
        import hashlib
        import random
        import struct

        # RADIUS包头
        code = 40  # Disconnect-Request
        identifier = random.randint(0, 255)
        length = 20  # 基础长度，后面会更新
        authenticator = b'\x00' * 16  # 临时认证字段

        # 属性
        attributes = b''

        # Acct-Session-Id (44)
        session_id_bytes = session_id.encode('utf-8')
        attr_length = 2 + len(session_id_bytes)
        attributes += struct.pack('!BB', 44, attr_length) + session_id_bytes

        # 更新总长度
        length = 20 + len(attributes)

        # 构建包（不包含最终的认证字段）
        packet_without_auth = struct.pack('!BBH', code, identifier, length) + authenticator + attributes

        # 计算Request Authenticator (MD5)
        md5 = hashlib.md5()
        md5.update(packet_without_auth)
        md5.update(secret)
        request_authenticator = md5.digest()

        # 构建最终包
        final_packet = struct.pack('!BBH', code, identifier, length) + request_authenticator + attributes

        return final_packet

    @action(detail=False, methods=['post'])
    def clear_online(self, request):
        """清在线 - 删除在线记录"""
        user_name = request.data.get('user_name')
        user_domain = request.data.get('user_domain')

        if not user_name or not user_domain:
            return Response({'error': '用户名和域名不能为空'}, status=400)

        # 查找在线记录
        online_records = OnlineRecord.objects.filter(
            user_name=user_name,
            user_domain=user_domain
        )

        if not online_records.exists():
            return Response({'error': '用户不在线'}, status=404)

        # 删除在线记录
        count = online_records.count()
        online_records.delete()

        return Response({
            'message': f'成功清除用户 {user_name}@{user_domain} 的在线记录，共 {count} 个会话'
        })
