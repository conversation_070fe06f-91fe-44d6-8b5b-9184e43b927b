#!/bin/bash

# Django后端Nuitka编译脚本
# 在web目录下运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查环境
check_environment() {
    print_step "检查编译环境..."
    
    if [[ ! -f "backend/main.py" ]]; then
        print_error "请在web目录下运行此脚本"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装或不可用"
        exit 1
    fi
    
    if [[ ! -f "docker_env/backend/Dockerfile_nuitka" ]]; then
        print_error "Dockerfile_nuitka 不存在"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 构建镜像
build_image() {
    print_step "构建Nuitka编译的后端镜像..."
    
    local proxy_args=""
    if [[ -n "${http_proxy:-}" ]]; then
        print_info "使用代理进行构建"
        proxy_args="--build-arg http_proxy=$http_proxy --build-arg https_proxy=$https_proxy --build-arg no_proxy=$no_proxy"
    fi
    
    if docker build \
        $proxy_args \
        -f docker_env/backend/Dockerfile_nuitka \
        -t ynyb-backend-nuitka:latest \
        .; then
        print_success "镜像构建成功！"
    else
        print_error "镜像构建失败"
        exit 1
    fi
}

# 主函数
main() {
    print_info "🚀 开始构建Django后端Nuitka镜像"
    
    check_environment
    build_image
    
    print_success "🎉 构建完成！"
    print_info "镜像名称: ynyb-backend-nuitka:latest"
    print_info "现在可以使用 docker-compose-integrated.yml 启动服务"
}

main "$@"
