# Django后端Nuitka编译版本的Dockerfile
FROM python:3.12-slim AS builder

# 接收代理参数
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置代理环境变量
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}

# 配置apt代理
RUN if [ -n "$http_proxy" ]; then \
        echo "Acquire::http::Proxy \"$http_proxy\";" > /etc/apt/apt.conf.d/01proxy; \
        echo "Acquire::https::Proxy \"$https_proxy\";" >> /etc/apt/apt.conf.d/01proxy; \
        echo "Acquire::ftp::Proxy \"$http_proxy\";" >> /etc/apt/apt.conf.d/01proxy; \
        echo "=== APT代理配置 ===" && \
        cat /etc/apt/apt.conf.d/01proxy; \
    else \
        echo "=== 未设置代理，使用清华源 ==="; \
    fi

# 配置源（清理冲突并设置正确的源）
RUN echo "=== 清理现有源配置 ===" && \
    rm -f /etc/apt/sources.list.d/* && \
    if [ -n "$http_proxy" ]; then \
        echo "=== 使用代理访问官方源 ===" && \
        echo "# 官方源配置（包含contrib和non-free）" > /etc/apt/sources.list && \
        echo "deb http://deb.debian.org/debian bookworm main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb http://deb.debian.org/debian bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb http://deb.debian.org/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
    else \
        echo "=== 配置清华源 ===" && \
        echo "# 清华大学源配置" > /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
        echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
    fi

# 验证源配置并安装编译依赖
RUN echo "=== 验证APT源配置 ===" && \
    echo "sources.list内容:" && cat /etc/apt/sources.list && \
    echo "=== 开始更新包列表 ===" && \
    apt-get update && \
    echo "=== 安装编译依赖 ===" && \
    apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    make \
    ccache \
    patchelf \
    chrpath \
    pkg-config \
    libmariadb-dev \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    libjpeg-dev \
    zlib1g-dev \
    && rm -rf /var/lib/apt/lists/* \
    && echo "=== 编译依赖安装完成 ==="

# 配置pip源
RUN if [ -n "$http_proxy" ]; then \
        echo "=== 使用代理访问官方PyPI ===" && \
        pip config set global.index-url https://pypi.org/simple && \
        pip config set global.proxy $http_proxy; \
    else \
        echo "=== 配置pip清华源 ===" && \
        pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
        pip config set install.trusted-host pypi.tuna.tsinghua.edu.cn; \
    fi && \
    echo "=== pip配置完成 ===" && \
    pip config list

# 设置工作目录
WORKDIR /build

# 首先复制requirements.txt（变化较少，利用缓存）
COPY backend/requirements.txt /build/

# 安装Python依赖（这层会被缓存，除非requirements.txt改变）
RUN pip install --no-cache-dir -r requirements.txt

# 安装Nuitka和相关工具（这层也会被缓存）
RUN pip install nuitka ordered-set

# 然后复制应用代码（变化较多，放在后面）
COPY backend/main.py /build/
COPY backend/manage.py /build/
COPY backend/application/ /build/application/
COPY backend/dvadmin/ /build/dvadmin/
COPY backend/conf/ /build/conf/
COPY backend/plugins/ /build/plugins/
COPY backend/utils/ /build/utils/
COPY backend/websocket/ /build/websocket/
COPY backend/logs/ /build/logs/
COPY backend/templates/ /build/templates/
COPY backend/static/ /build/static/

# Nuitka编译 - Django应用编译（修复版本）
RUN echo "=== 开始Nuitka编译Django后端 ===" && \
    python -m nuitka \
    --standalone \
    --onefile \
    --assume-yes-for-downloads \
    --output-filename=backend_server \
    --include-data-dir=templates=templates \
    --include-data-dir=static=static \
    --include-data-dir=plugins=plugins \
    --include-package=application \
    --include-package=dvadmin \
    --include-package=utils \
    --include-package=websocket \
    --include-package=logs \
    --include-package=django \
    --include-package=rest_framework \
    --include-package=uvicorn \
    --include-package=asyncio \
    --include-package=logging \
    --include-package=configparser \
    --include-package=json \
    --include-package=threading \
    --include-package=concurrent.futures \
    --module-parameter=django-settings-module=application.settings \
    --enable-plugin=anti-bloat \
    --remove-output \
    --no-pyi-file \
    --lto=no \
    --jobs=$(nproc) \
    --nofollow-import-to=pytest,unittest,test,tests \
    --nofollow-import-to=setuptools,distutils,pip \
    --nofollow-import-to=mysqlclient,psycopg2 \
    main.py && \
    echo "=== Nuitka编译完成 ==="

# 验证编译结果
RUN echo "=== 验证编译结果 ===" && \
    ls -la && \
    if [ -f "backend_server" ]; then \
        echo "=== 找到编译的可执行文件 ===" && \
        ls -lh backend_server && \
        test -x backend_server && \
        echo "=== 编译验证完成 ==="; \
    else \
        echo "=== 错误：未找到编译结果 ===" && \
        exit 1; \
    fi

# 运行时镜像 - 使用Ubuntu 24.04轻量镜像
FROM ubuntu:24.04

# 设置环境变量支持UTF-8
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONIOENCODING=utf-8

# 安装运行时必要的依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ca-certificates \
    locales \
    libmariadb3 \
    libpq5 \
    libjpeg-turbo8 \
    curl \
    && locale-gen en_US.UTF-8 \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r backend && useradd -r -g backend backend

# 创建应用目录和logs目录
RUN mkdir -p /backend/logs && \
    mkdir -p /backend/media && \
    chown backend:backend /backend && \
    chown backend:backend /backend/logs && \
    chown backend:backend /backend/media

# 复制编译后的可执行文件和相关文件
COPY --from=builder /build/backend_server /backend/backend_server
COPY --from=builder /build/templates/ /backend/templates/
COPY --from=builder /build/static/ /backend/static/
COPY --from=builder /build/conf/ /backend/conf/

# 设置文件权限
RUN chmod 755 /backend/backend_server && \
    chown -R backend:backend /backend && \
    ls -la /backend/ && \
    echo "=== 权限设置完成 ==="

# 切换到非root用户
USER backend

# 设置工作目录
WORKDIR /backend

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# 启动命令
ENTRYPOINT ["/backend/backend_server"]
