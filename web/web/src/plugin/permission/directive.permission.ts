import permissionUtil from './func.permission'
export default {
  mounted (el:any, binding:any) {
    const { value } = binding
    const hasPermission = permissionUtil.hasPermissions(value)
    if (!hasPermission) {
      // 使用更安全的方式隐藏元素
      el.style.display = 'none'
      el.setAttribute('aria-hidden', 'true')
    }
  },
  updated (el:any, binding:any) {
    const { value } = binding
    const hasPermission = permissionUtil.hasPermissions(value)
    if (!hasPermission) {
      el.style.display = 'none'
      el.setAttribute('aria-hidden', 'true')
    } else {
      el.style.display = ''
      el.removeAttribute('aria-hidden')
    }
  }
}
