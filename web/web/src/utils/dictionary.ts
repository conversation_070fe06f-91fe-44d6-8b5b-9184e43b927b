import { request } from './service'
import { DictionaryStore } from '/@/stores/dictionary'

export interface DictConfig {
  url?: string
  data?: Array<any>
  value?: string
  label?: string
}

export function dictionaryConfig(config: DictConfig) {
  return {
    url: config.url,
    value: config.value || 'value',
    label: config.label || 'label',
    data: config.data || [],
    async getData() {
      if (this.url) {
        const res = await request({
          url: this.url,
          method: 'get'
        })
        return res.data
      }
      return this.data
    }
  }
}

// 通过字符串key获取字典数据的函数
export function dictionary(key: string) {
  const dictionaryStore = DictionaryStore()
  return dictionaryStore.data[key] || []
}

export const dict = dictionaryConfig
